import {Entity, model, property, hasMany} from '@loopback/repository';
import {IncidentUnderlyingCauseType} from './incident-underlying-cause-type.model';

@model()
export class IncidentUnderlyingCause extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  description?: string;

  @hasMany(() => IncidentUnderlyingCauseType)
  incidentUnderlyingCauseTypes: IncidentUnderlyingCauseType[];

  constructor(data?: Partial<IncidentUnderlyingCause>) {
    super(data);
  }
}

export interface IncidentUnderlyingCauseRelations {
  // describe navigational properties here
}

export type IncidentUnderlyingCauseWithRelations = IncidentUnderlyingCause & IncidentUnderlyingCauseRelations;
