import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  IncidentRootCauseType,
  IncidentRootCauseDescription,
} from '../models';
import {IncidentRootCauseTypeRepository} from '../repositories';

export class IncidentRootCauseTypeIncidentRootCauseDescriptionController {
  constructor(
    @repository(IncidentRootCauseTypeRepository) protected incidentRootCauseTypeRepository: IncidentRootCauseTypeRepository,
  ) { }

  @get('/incident-root-cause-types/{id}/incident-root-cause-descriptions', {
    responses: {
      '200': {
        description: 'Array of IncidentRootCauseType has many IncidentRootCauseDescription',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(IncidentRootCauseDescription)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<IncidentRootCauseDescription>,
  ): Promise<IncidentRootCauseDescription[]> {
    return this.incidentRootCauseTypeRepository.incidentRootCauseDescriptions(id).find(filter);
  }

  @post('/incident-root-cause-types/{id}/incident-root-cause-descriptions', {
    responses: {
      '200': {
        description: 'IncidentRootCauseType model instance',
        content: {'application/json': {schema: getModelSchemaRef(IncidentRootCauseDescription)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof IncidentRootCauseType.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(IncidentRootCauseDescription, {
            title: 'NewIncidentRootCauseDescriptionInIncidentRootCauseType',
            exclude: ['id'],
            optional: ['incidentRootCauseTypeId']
          }),
        },
      },
    }) incidentRootCauseDescription: Omit<IncidentRootCauseDescription, 'id'>,
  ): Promise<IncidentRootCauseDescription> {
    return this.incidentRootCauseTypeRepository.incidentRootCauseDescriptions(id).create(incidentRootCauseDescription);
  }

  @patch('/incident-root-cause-types/{id}/incident-root-cause-descriptions', {
    responses: {
      '200': {
        description: 'IncidentRootCauseType.IncidentRootCauseDescription PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(IncidentRootCauseDescription, {partial: true}),
        },
      },
    })
    incidentRootCauseDescription: Partial<IncidentRootCauseDescription>,
    @param.query.object('where', getWhereSchemaFor(IncidentRootCauseDescription)) where?: Where<IncidentRootCauseDescription>,
  ): Promise<Count> {
    return this.incidentRootCauseTypeRepository.incidentRootCauseDescriptions(id).patch(incidentRootCauseDescription, where);
  }

  @del('/incident-root-cause-types/{id}/incident-root-cause-descriptions', {
    responses: {
      '200': {
        description: 'IncidentRootCauseType.IncidentRootCauseDescription DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(IncidentRootCauseDescription)) where?: Where<IncidentRootCauseDescription>,
  ): Promise<Count> {
    return this.incidentRootCauseTypeRepository.incidentRootCauseDescriptions(id).delete(where);
  }
}
