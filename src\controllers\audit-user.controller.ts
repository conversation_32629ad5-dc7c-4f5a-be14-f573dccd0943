import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  Audit,
  User,
} from '../models';
import {AuditRepository} from '../repositories';

export class AuditUserController {
  constructor(
    @repository(AuditRepository)
    public auditRepository: AuditRepository,
  ) { }

  @get('/audits/{id}/user', {
    responses: {
      '200': {
        description: 'User belonging to <PERSON><PERSON>',
        content: {
          'application/json': {
            schema: getModelSchemaRef(User),
          },
        },
      },
    },
  })
  async getUser(
    @param.path.string('id') id: typeof Audit.prototype.id,
  ): Promise<User> {
    return this.auditRepository.assignedTo(id);
  }
}
