import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import { Audit } from '../models';
import { AuditRepository, ActionRepository } from '../repositories';

export class AuditController {
  constructor(
    @repository(AuditRepository)
    public auditRepository: AuditRepository,

    @repository(ActionRepository)
    public actionRepository: ActionRepository,
  ) { }

  @post('/audits')
  @response(200, {
    description: 'Audit model instance',
    content: { 'application/json': { schema: getModelSchemaRef(Audit) } },
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Audit, {
            title: 'NewAudit',
            exclude: ['id'],
          }),
        },
      },
    })
    audit: Omit<Audit, 'id'>,
  ): Promise<Audit> {
    return this.auditRepository.create(audit);
  }

  @get('/audits/count')
  @response(200, {
    description: 'Audit model count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async count(
    @param.where(Audit) where?: Where<Audit>,
  ): Promise<Count> {
    return this.auditRepository.count(where);
  }

  @get('/audits')
  @response(200, {
    description: 'Array of Audit model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Audit, { includeRelations: true }),
        },
      },
    },
  })
  async find(
    @param.filter(Audit) filter?: Filter<Audit>,
  ): Promise<Audit[]> {
    return this.auditRepository.find(filter);
  }

  @patch('/audits')
  @response(200, {
    description: 'Audit PATCH success count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Audit, { partial: true }),
        },
      },
    })
    audit: Audit,
    @param.where(Audit) where?: Where<Audit>,
  ): Promise<Count> {
    return this.auditRepository.updateAll(audit, where);
  }

  @get('/audits/{id}')
  @response(200, {
    description: 'Audit model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(Audit, { includeRelations: true }),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(Audit, { exclude: 'where' }) filter?: FilterExcludingWhere<Audit>
  ): Promise<Audit> {
    return this.auditRepository.findById(id, filter);
  }

  @patch('/audits/{id}')
  @response(204, {
    description: 'Audit PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Audit, { partial: true }),
        },
      },
    })
    audit: Audit,
  ): Promise<void> {
    await this.auditRepository.updateById(id, audit);
  }

  @put('/audits/{id}')
  @response(204, {
    description: 'Audit PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() audit: Audit,
  ): Promise<void> {
    await this.auditRepository.replaceById(id, audit);
  }

  @del('/audits/{id}')
  @response(204, {
    description: 'Audit DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.auditRepository.deleteById(id);
    await this.actionRepository.deleteAll({ objectId: id })
  }
}
