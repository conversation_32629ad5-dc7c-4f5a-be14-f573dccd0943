import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
  HttpErrors,
} from '@loopback/rest';
import { ReportIncident, Action } from '../models';
import { ReportIncidentRepository, ActionRepository, UserRepository, UserLocationRoleRepository, LocationOneRepository, LocationTwoRepository, LocationThreeRepository, LocationFourRepository } from '../repositories';

import { inject } from '@loopback/core';
import { SecurityBindings, securityId, UserProfile } from '@loopback/security';
import { authenticate } from '@loopback/authentication';
import moment from 'moment';
import { SqsService } from '../services/sqs-service.service';

export class ReportIncidentController {
  constructor(
    @repository(ActionRepository)
    public actionRepository: ActionRepository,
    @repository(ReportIncidentRepository)
    public reportIncidentRepository: ReportIncidentRepository,
    @repository(UserLocationRoleRepository)
    public userLocationRoleRepository: UserLocationRoleRepository,
    @repository(LocationOneRepository)
    public locationOneRepository: LocationOneRepository,
    @repository(LocationTwoRepository)
    public locationTwoRepository: LocationTwoRepository,
    @repository(LocationThreeRepository)
    public locationThreeRepository: LocationThreeRepository,
    @repository(LocationFourRepository)
    public locationFourRepository: LocationFourRepository,
    @repository(UserRepository)
    public userRepository: UserRepository,
    @inject('services.SqsService')
    public sqsService: SqsService,
  ) { }

  @authenticate('jwt')
  @post('/report-incidents')
  @response(200, {
    description: 'ReportIncident model instance',
    content: { 'application/json': { schema: getModelSchemaRef(ReportIncident) } },
  })
  async create(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ReportIncident, {
            title: 'NewReportIncident',
            exclude: ['id'],
          }),
        },
      },
    })
    reportIncident: Omit<ReportIncident, 'id'>,
  ): Promise<ReportIncident> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });
    //generate id
    const count = await this.reportIncidentRepository.count();
    reportIncident.maskId = `INC-${moment().format('YYMMDD')}-${String(count.count + 1).padStart(4, '0')}`;
    if (user) {
      reportIncident.userId = user.id;
      reportIncident.status = 'Reported'
      return this.reportIncidentRepository.create(reportIncident);
    } else {
      throw new HttpErrors.NotFound('User not Found')
    }

  }

  @get('/report-incidents/count')
  @response(200, {
    description: 'ReportIncident model count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async count(
    @param.where(ReportIncident) where?: Where<ReportIncident>,
  ): Promise<Count> {
    return this.reportIncidentRepository.count(where);
  }

  @get('/report-incidents')
  @response(200, {
    description: 'Array of ReportIncident model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ReportIncident, { includeRelations: true }),
        },
      },
    },
  })
  @authenticate('jwt')
  async find(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.filter(ReportIncident) filter?: Filter<ReportIncident>,
  ): Promise<ReportIncident[]> {
    
    return this.reportIncidentRepository.find(filter);
  }






  @patch('/report-incidents')
  @response(200, {
    description: 'ReportIncident PATCH success count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ReportIncident, { partial: true }),
        },
      },
    })
    reportIncident: ReportIncident,
    @param.where(ReportIncident) where?: Where<ReportIncident>,
  ): Promise<Count> {
    return this.reportIncidentRepository.updateAll(reportIncident, where);
  }

  @get('/report-incidents/{id}')
  @response(200, {
    description: 'ReportIncident model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(ReportIncident, { includeRelations: true }),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(ReportIncident, { exclude: 'where' }) filter?: FilterExcludingWhere<ReportIncident>
  ): Promise<ReportIncident> {
    return this.reportIncidentRepository.findById(id, filter);
  }

  @patch('/report-incidents/{id}')
  @response(204, {
    description: 'ReportIncident PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ReportIncident, { partial: true }),
        },
      },
    })
    reportIncident: ReportIncident,
  ): Promise<void> {

    await this.reportIncidentRepository.updateById(id, reportIncident);
  }

  @authenticate('jwt')
  @patch('/report-incidents-review/{id}')
  @response(204, {
    description: 'ReportIncident PATCH success',
  })
  async updateReviewById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ReportIncident, { partial: true }),
        },
      },
    })
    reportIncident: ReportIncident,
  ): Promise<void> {
    reportIncident.status = 'Reviewed'

    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });
    let actionItem = []

    if (reportIncident.riskControl) {
      const controlMeasures = reportIncident.riskControl.controlMeasures || [];
      const riskAssessment = reportIncident.riskControl.riskAssessment || [];
      if (controlMeasures && controlMeasures.length > 0) {
        const modifiedActions = controlMeasures.map((i: any) => {
          return {
            application: "INCIDENT",
            actionType: "take_actions_control",
            description: '',
            actionToBeTaken: i.controlMeasures,
            dueDate: i.completionDate,
            status: 'open',
            createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
            objectId: id,
            submittedById: user?.id,
            assignedToId: i.personResponsible

          }
        })
        await this.actionRepository.createAll(modifiedActions)
      }

      if (riskAssessment && riskAssessment.length > 0) {
        const modifiedActions = riskAssessment.map((i: any) => {
          return {
            application: "INCIDENT",
            actionType: "take_actions_ra",
            description: '',
            actionToBeTaken: i.name,
            dueDate: i.completionDate,
            status: 'open',
            createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
            objectId: id,
            submittedById: user?.id,
            assignedToId: i.personResponsible

          }
        })
        await this.actionRepository.createAll(modifiedActions)
      }
    }
    // actionItem = [...actionItem,
    //   {
    //     application: "AIR",
    //     actionType: "air_surveyor",

    //     description: airReportData.description,
    //     dueDate: '',

    //     status: "open",
    //     createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
    //     objectId: airReportData.id,
    //     submittedById: user?.id,
    //     assignedToId: airReportData.reviewerId
    //   }]

    await this.reportIncidentRepository.updateById(id, reportIncident);
  }


  @authenticate('jwt')
  @patch('/report-incidents-investigation-verify/{id}/{action_id}')
  @response(204, {
    description: 'ReportIncident PATCH success',
  })
  async updateInvesigationVerifyById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('action_id') action_id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ReportIncident, { partial: true }),
        },
      },
    })
    reportIncident: ReportIncident,
  ): Promise<void> {
    reportIncident.status = 'Investigation Completed'
    await this.actionRepository.updateById(action_id, { status: 'submitted' })
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });
    const reportIncidentData = await this.reportIncidentRepository.findById(id)
    let actionItem = []

    if (reportIncidentData.investigationStep && reportIncidentData.investigationStep.dynamicForm && reportIncidentData.investigationStep.dynamicForm.actions) {
      const actions = reportIncidentData.investigationStep.dynamicForm.actions || [];

      if (actions && actions.length > 0) {
        const modifiedActions = actions.map((i: any) => {
          return {
            application: "INCIDENT",
            actionType: "take_investigation_actions",
            description: reportIncidentData.description,
            dueDate: i.date,
            status: 'open',
            actionToBeTaken: i.description,
            createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
            objectId: id,
            submittedById: user?.id,
            assignedToId: i.personResponsible

          }
        })
        await this.actionRepository.createAll(modifiedActions)
      }


    }
    // actionItem = [...actionItem,
    //   {
    //     application: "AIR",
    //     actionType: "air_surveyor",

    //     description: airReportData.description,
    //     dueDate: '',

    //     status: "open",
    //     createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
    //     objectId: airReportData.id,
    //     submittedById: user?.id,
    //     assignedToId: airReportData.reviewerId
    //   }]

    await this.reportIncidentRepository.updateById(id, reportIncident);
  }


  @authenticate('jwt')
  @patch('/report-incidents-investigation-reinvestigate/{id}/{action_id}')
  @response(204, {
    description: 'ReportIncident PATCH success',
  })
  async updateReInvesigationVerifyById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('action_id') action_id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ReportIncident, { partial: true }),
        },
      },
    })
    reportIncident: ReportIncident,
  ): Promise<void> {
    reportIncident.status = 'Reinvestigate'

    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });

    await this.actionRepository.updateById(action_id, { status: 'submitted' })

    await this.reportIncidentRepository.updateById(id, reportIncident);
  }


  @authenticate('jwt')
  @patch('/report-incidents-investigation/{id}')
  @response(204, {
    description: 'ReportIncident PATCH success',
  })
  async updateInvestigationById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ReportIncident, { partial: true }),
        },
      },
    })
    reportIncident: ReportIncident,
  ): Promise<void> {

    reportIncident.status = 'Investigated'
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });
    const reportData = await this.reportIncidentRepository.findById(id)

    const actionItem = {
      application: "INCIDENT",
      actionType: "verify_investigation_actions",

      description: reportData.description,
      dueDate: '',

      status: "open",
      createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
      objectId: id,
      submittedById: user?.id,
      assignedToId: reportData.incidentOwnerId
    }

    await this.actionRepository.create(actionItem)
    await this.reportIncidentRepository.updateById(id, reportIncident);
  }


  @authenticate('jwt')
  @patch('/save-report-incidents-investigation/{id}')
  @response(204, {
    description: 'ReportIncident PATCH success',
  })
  async updateSaveInvestigationById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ReportIncident, { partial: true }),
        },
      },
    })
    reportIncident: ReportIncident,
  ): Promise<void> {


    await this.reportIncidentRepository.updateById(id, reportIncident);
  }

  @authenticate('jwt')
  @patch('/report-incidents-investigate/{id}')
  @response(204, {
    description: 'ReportIncident PATCH success',
  })
  async updateInvestigateById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ReportIncident, { partial: true }),
        },
      },
    })
    reportIncident: ReportIncident,
  ): Promise<void> {
    reportIncident.status = 'Under Investigation'
    if (reportIncident.investigationStatus) {

    } else {
      reportIncident.status = 'Tracked'
    }
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });



    await this.reportIncidentRepository.updateById(id, reportIncident);
  }

  @authenticate('jwt')
  @patch('report-incident/actions/{id}')
  @response(204, {
    description: 'Action PATCH success',
  })
  async updateActionsById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Action, { partial: true }),
        },
      },
    })
    action: Action,
  ): Promise<void> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });
    const reportData = await this.reportIncidentRepository.findById(action.objectId)
    action.status = 'submitted'
    if (action.actionType === 'take_actions_ra' || action.actionType === 'take_actions_control' || action.actionType === 'retake_actions' || action.actionType === 'take_investigation_actions') {
      delete action.actionType

      await this.reportIncidentRepository.updateById(action.objectId, { evidence: action.uploads })

      const actionItem = {
        application: "INCIDENT",
        actionType: "verify_actions",
        comments: action.comments,
        actionTaken: action.actionTaken,
        actionToBeTaken: action.actionToBeTaken,
        description: action.description,
        dueDate: action.dueDate,
        uploads: action.uploads,
        status: "open",
        createdDate: action.createdDate,
        objectId: action.objectId,
        submittedById: user?.id,
        assignedToId: reportData.userId
      }


      if (reportData.userId && action.objectId) {

        const observationDetails = await this.reportIncidentRepository.findById(action.objectId, {
          include: [
            { relation: 'locationOne' },
            { relation: 'locationTwo' },
            { relation: 'locationThree' },
            { relation: 'locationFour' },
            { relation: 'locationFive' },
            { relation: 'locationSix' },

          ]
        })
        const reviewerDetails = await this.userRepository.findById(reportData.userId)

        const locationOneName = (observationDetails as any).locationOne?.name;
        const locationTwoName = (observationDetails as any).locationTwo?.name;
        const locationThreeName = (observationDetails as any).locationThree?.name;
        const locationFourName = (observationDetails as any).locationFour?.name;
        const locationFiveName = (observationDetails as any).locationFive?.name;
        const locationSixName = (observationDetails as any).locationSix?.name;

        const mailSubject = `Verify Action: ${locationFiveName}, ${locationSixName}`;
        const mailBody = `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Observation Report Email</title>
        </head>
        <body>
            <h1>Observation Report</h1>
           
            <p><strong>Name of Assignee: </strong> ${user?.firstName}</p>
            <p><strong>Location: </strong> ${locationOneName} &gt; ${locationTwoName} &gt; ${locationThreeName} &gt; ${locationFourName} &gt; ${locationFiveName} &gt; ${locationSixName}</p>
           
            <p><strong>Rectification action taken by Assignee: </strong> ${action.actionTaken}</p>
            <p><strong>Evidence:</strong></p>
            <ul>
                ${action.uploads?.map(upload => `<li><a href="${process.env.STATIC_URL}/docs/${encodeURIComponent(upload)}">${upload}</a></li>`) ?? ''}
            </ul>
        </body>
        </html>`;


        if (reviewerDetails) { this.sqsService.sendMessage(reviewerDetails, mailSubject, mailBody) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
      }

      await this.actionRepository.create(actionItem)

    }

    if (action.actionType === 'approve') {

      if (action.objectId) {

        const observationDetails = await this.reportIncidentRepository.findById(action.objectId, {
          include: [
            { relation: 'locationOne' },
            { relation: 'locationTwo' },
            { relation: 'locationThree' },
            { relation: 'locationFour' },
            { relation: 'locationFive' },
            { relation: 'locationSix' },

          ]
        })



      }

    }

    if (action.actionType === 'reject') {

      const actionItem = {
        application: "INCIDENT",
        actionType: "retake_actions",
        comments: action.comments,
        actionTaken: action.actionTaken,
        actionToBeTaken: action.actionToBeTaken,
        description: action.description,
        dueDate: action.dueDate,
        uploads: action.uploads,
        status: "open",
        createdDate: action.createdDate,
        objectId: action.objectId,
        submittedById: user?.id,
        assignedToId: action.assignedToId
      }

      await this.actionRepository.create(actionItem)
    }
    delete action.assignedToId
    await this.actionRepository.updateById(id, action);
  }

  @authenticate('cognito-jwt', 'jwt')
  @patch('/report-incidents-investigate-close/{id}')
  @response(204, {
    description: 'ReportIncident PATCH success',
  })
  async updateInvestigateCloseById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ReportIncident, { partial: true }),
        },
      },
    })
    reportIncident: ReportIncident,
  ): Promise<void> {
    reportIncident.status = 'Tracked'

    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });



    await this.reportIncidentRepository.updateById(id, reportIncident);
  }

  @patch('/save-report-incidents/{id}')
  @response(204, {
    description: 'ReportIncident PATCH success',
  })
  async updateByIdWithoutInvestigation(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ReportIncident, { partial: true }),
        },
      },
    })
    reportIncident: ReportIncident,
  ): Promise<void> {

    await this.reportIncidentRepository.updateById(id, reportIncident);
  }

  @put('/report-incidents/{id}')
  @response(204, {
    description: 'ReportIncident PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() reportIncident: ReportIncident,
  ): Promise<void> {
    await this.reportIncidentRepository.replaceById(id, reportIncident);
  }

  @del('/report-incidents/{id}')
  @response(204, {
    description: 'ReportIncident DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {

    await this.actionRepository.deleteAll({ objectId: id });

    
    await this.reportIncidentRepository.deleteById(id);
  }
}
