import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {RiskCategory, RiskCategoryRelations} from '../models';

export class RiskCategoryRepository extends DefaultCrudRepository<
  RiskCategory,
  typeof RiskCategory.prototype.id,
  RiskCategoryRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(RiskCategory, dataSource);
  }
}
