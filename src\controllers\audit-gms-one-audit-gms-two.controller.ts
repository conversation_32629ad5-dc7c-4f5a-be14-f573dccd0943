import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  AuditGmsOne,
  AuditGmsTwo,
} from '../models';
import {AuditGmsOneRepository} from '../repositories';

export class AuditGmsOneAuditGmsTwoController {
  constructor(
    @repository(AuditGmsOneRepository) protected auditGmsOneRepository: AuditGmsOneRepository,
  ) { }

  @get('/audit-gms-ones/{id}/audit-gms-twos', {
    responses: {
      '200': {
        description: 'Array of AuditGmsOne has many AuditGmsTwo',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(AuditGmsTwo)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<AuditGmsTwo>,
  ): Promise<AuditGmsTwo[]> {
    return this.auditGmsOneRepository.auditGmsTwos(id).find(filter);
  }

  @post('/audit-gms-ones/{id}/audit-gms-twos', {
    responses: {
      '200': {
        description: 'AuditGmsOne model instance',
        content: {'application/json': {schema: getModelSchemaRef(AuditGmsTwo)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof AuditGmsOne.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AuditGmsTwo, {
            title: 'NewAuditGmsTwoInAuditGmsOne',
            exclude: ['id'],
            optional: ['auditGmsOneId']
          }),
        },
      },
    }) auditGmsTwo: Omit<AuditGmsTwo, 'id'>,
  ): Promise<AuditGmsTwo> {
    return this.auditGmsOneRepository.auditGmsTwos(id).create(auditGmsTwo);
  }

  @patch('/audit-gms-ones/{id}/audit-gms-twos', {
    responses: {
      '200': {
        description: 'AuditGmsOne.AuditGmsTwo PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AuditGmsTwo, {partial: true}),
        },
      },
    })
    auditGmsTwo: Partial<AuditGmsTwo>,
    @param.query.object('where', getWhereSchemaFor(AuditGmsTwo)) where?: Where<AuditGmsTwo>,
  ): Promise<Count> {
    return this.auditGmsOneRepository.auditGmsTwos(id).patch(auditGmsTwo, where);
  }

  @del('/audit-gms-ones/{id}/audit-gms-twos', {
    responses: {
      '200': {
        description: 'AuditGmsOne.AuditGmsTwo DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(AuditGmsTwo)) where?: Where<AuditGmsTwo>,
  ): Promise<Count> {
    return this.auditGmsOneRepository.auditGmsTwos(id).delete(where);
  }
}
