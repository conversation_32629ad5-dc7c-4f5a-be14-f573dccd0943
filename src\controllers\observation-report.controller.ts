import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import { ObservationReport, Action } from '../models';
import { ObservationReportRepository, ActionRepository, UserRepository } from '../repositories';

import { authenticate } from '@loopback/authentication';


@authenticate('jwt')
export class ObservationReportController {
  constructor(
    @repository(ObservationReportRepository)
    public observationReportRepository: ObservationReportRepository,
    @repository(ActionRepository)
    public actionRepository: ActionRepository,
    @repository(UserRepository)
    public userRepository: UserRepository,
  ) { }

  @post('/observation-reports')
  @response(200, {
    description: 'ObservationReport model instance',
    content: { 'application/json': { schema: getModelSchemaRef(ObservationReport) } },
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ObservationReport, {
            title: 'NewObservationReport',
            exclude: ['id'],
          }),
        },
      },
    })
    observationReport: Omit<ObservationReport, 'id'>,
  ): Promise<ObservationReport> {
    const obsReport = await this.observationReportRepository.create(observationReport);
    const actionItem = {
      application: "Observation",
      action_type: "action_owner",
      comments: "",
      description: obsReport.description,
      dueDate: obsReport.dueDate,
      status: "open",
      createdDate: obsReport.created,
      objectId: obsReport.id,
      submittedById: obsReport.actionOwnerId,
      assignedToId: obsReport.actionOwnerId
    }
    await this.actionRepository.create(actionItem)
    return obsReport;
  }

  @get('/observation-reports/count')
  @response(200, {
    description: 'ObservationReport model count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async count(
    @param.where(ObservationReport) where?: Where<ObservationReport>,
  ): Promise<Count> {
    return this.observationReportRepository.count(where);
  }

  @get('/observation-reports')
  @response(200, {
    description: 'Array of ObservationReport model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ObservationReport, { includeRelations: true }),
        },
      },
    },
  })
  async find(
    @param.filter(ObservationReport) filter?: Filter<ObservationReport>,
  ): Promise<ObservationReport[]> {
    return this.observationReportRepository.find(filter);
  }


  @patch('/observation-reports')
  @response(200, {
    description: 'ObservationReport PATCH success count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ObservationReport, { partial: true }),
        },
      },
    })
    observationReport: ObservationReport,
    @param.where(ObservationReport) where?: Where<ObservationReport>,
  ): Promise<Count> {
    return this.observationReportRepository.updateAll(observationReport, where);
  }

  @get('/observation-reports/{id}')
  async findById(
    @param.path.string('id') id: string,
    @param.filter(ObservationReport, { exclude: 'where' }) filter?: FilterExcludingWhere<ObservationReport>
  ): Promise<any> {
    const obsData: any = await this.observationReportRepository.findById(id, filter);
    obsData.submittedBy = obsData.submittedId ? await this.userRepository.findById(obsData.submittedId) : '';
    obsData.actionBy = obsData.actionOwnerId ? await this.userRepository.findById(obsData.actionOwnerId) : '';
    obsData.reviewBy = obsData.reviewerId ? await this.userRepository.findById(obsData.reviewerId) : '';

    return obsData;

  }

  @patch('/observation-reports/{id}')
  @response(204, {
    description: 'ObservationReport PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ObservationReport, { partial: true }),
        },
      },
    })
    observationReport: ObservationReport,
  ): Promise<void> {
    await this.observationReportRepository.updateById(id, observationReport);
  }

  @put('/observation-reports/{id}')
  @response(204, {
    description: 'ObservationReport PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() observationReport: ObservationReport,
  ): Promise<void> {
    await this.observationReportRepository.replaceById(id, observationReport);
  }

  @del('/observation-reports/{id}')
  @response(204, {
    description: 'ObservationReport DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {

    await this.actionRepository.deleteAll({ objectId: id });

    await this.observationReportRepository.deleteById(id);
  }
}
