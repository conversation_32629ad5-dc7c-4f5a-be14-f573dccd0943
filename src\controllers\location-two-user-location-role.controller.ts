import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  LocationTwo,
  UserLocationRole,
} from '../models';
import {LocationTwoRepository} from '../repositories';

export class LocationTwoUserLocationRoleController {
  constructor(
    @repository(LocationTwoRepository) protected locationTwoRepository: LocationTwoRepository,
  ) { }

  @get('/location-twos/{id}/user-location-roles', {
    responses: {
      '200': {
        description: 'Array of LocationTwo has many UserLocationRole',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(UserLocationRole)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<UserLocationRole>,
  ): Promise<UserLocationRole[]> {
    return this.locationTwoRepository.userLocationRoles(id).find(filter);
  }

  @post('/location-twos/{id}/user-location-roles', {
    responses: {
      '200': {
        description: 'LocationTwo model instance',
        content: {'application/json': {schema: getModelSchemaRef(UserLocationRole)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof LocationTwo.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(UserLocationRole, {
            title: 'NewUserLocationRoleInLocationTwo',
            exclude: ['id'],
            optional: ['locationTwoId']
          }),
        },
      },
    }) userLocationRole: Omit<UserLocationRole, 'id'>,
  ): Promise<UserLocationRole> {
    return this.locationTwoRepository.userLocationRoles(id).create(userLocationRole);
  }

  @patch('/location-twos/{id}/user-location-roles', {
    responses: {
      '200': {
        description: 'LocationTwo.UserLocationRole PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(UserLocationRole, {partial: true}),
        },
      },
    })
    userLocationRole: Partial<UserLocationRole>,
    @param.query.object('where', getWhereSchemaFor(UserLocationRole)) where?: Where<UserLocationRole>,
  ): Promise<Count> {
    return this.locationTwoRepository.userLocationRoles(id).patch(userLocationRole, where);
  }

  @del('/location-twos/{id}/user-location-roles', {
    responses: {
      '200': {
        description: 'LocationTwo.UserLocationRole DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(UserLocationRole)) where?: Where<UserLocationRole>,
  ): Promise<Count> {
    return this.locationTwoRepository.userLocationRoles(id).delete(where);
  }
}
