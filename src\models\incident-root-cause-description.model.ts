import {Entity, model, property} from '@loopback/repository';

@model()
export class IncidentRootCauseDescription extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'string',
  })
  incidentRootCauseTypeId?: string;

  constructor(data?: Partial<IncidentRootCauseDescription>) {
    super(data);
  }
}

export interface IncidentRootCauseDescriptionRelations {
  // describe navigational properties here
}

export type IncidentRootCauseDescriptionWithRelations = IncidentRootCauseDescription & IncidentRootCauseDescriptionRelations;
