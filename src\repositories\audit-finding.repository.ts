import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {AuditFinding, AuditFindingRelations, AuditGmsThree, Audit, User} from '../models';
import {AuditGmsThreeRepository} from './audit-gms-three.repository';
import {AuditRepository} from './audit.repository';
import {UserRepository} from './user.repository';

export class AuditFindingRepository extends DefaultCrudRepository<
  AuditFinding,
  typeof AuditFinding.prototype.id,
  AuditFindingRelations
> {

  public readonly auditGmsThree: BelongsToAccessor<AuditGmsThree, typeof AuditFinding.prototype.id>;

  public readonly audit: BelongsToAccessor<Audit, typeof AuditFinding.prototype.id>;

  public readonly assignedTo: BelongsToAccessor<User, typeof AuditFinding.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('AuditGmsThreeRepository') protected auditGmsThreeRepositoryGetter: Getter<AuditGmsThreeRepository>, @repository.getter('AuditRepository') protected auditRepositoryGetter: Getter<AuditRepository>, @repository.getter('UserRepository') protected userRepositoryGetter: Getter<UserRepository>,
  ) {
    super(AuditFinding, dataSource);
    this.assignedTo = this.createBelongsToAccessorFor('assignedTo', userRepositoryGetter,);
    this.registerInclusionResolver('assignedTo', this.assignedTo.inclusionResolver);
    this.audit = this.createBelongsToAccessorFor('audit', auditRepositoryGetter,);
    this.registerInclusionResolver('audit', this.audit.inclusionResolver);
    this.auditGmsThree = this.createBelongsToAccessorFor('auditGmsThree', auditGmsThreeRepositoryGetter,);
    this.registerInclusionResolver('auditGmsThree', this.auditGmsThree.inclusionResolver);
  }
}
