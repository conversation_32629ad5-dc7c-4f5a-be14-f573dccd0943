import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  IncidentUnderlyingCause,
  IncidentUnderlyingCauseType,
} from '../models';
import {IncidentUnderlyingCauseRepository} from '../repositories';

export class IncidentUnderlyingCauseIncidentUnderlyingCauseTypeController {
  constructor(
    @repository(IncidentUnderlyingCauseRepository) protected incidentUnderlyingCauseRepository: IncidentUnderlyingCauseRepository,
  ) { }

  @get('/incident-underlying-causes/{id}/incident-underlying-cause-types', {
    responses: {
      '200': {
        description: 'Array of IncidentUnderlyingCause has many IncidentUnderlyingCauseType',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(IncidentUnderlyingCauseType)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<IncidentUnderlyingCauseType>,
  ): Promise<IncidentUnderlyingCauseType[]> {
    return this.incidentUnderlyingCauseRepository.incidentUnderlyingCauseTypes(id).find(filter);
  }

  @post('/incident-underlying-causes/{id}/incident-underlying-cause-types', {
    responses: {
      '200': {
        description: 'IncidentUnderlyingCause model instance',
        content: {'application/json': {schema: getModelSchemaRef(IncidentUnderlyingCauseType)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof IncidentUnderlyingCause.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(IncidentUnderlyingCauseType, {
            title: 'NewIncidentUnderlyingCauseTypeInIncidentUnderlyingCause',
            exclude: ['id'],
            optional: ['incidentUnderlyingCauseId']
          }),
        },
      },
    }) incidentUnderlyingCauseType: Omit<IncidentUnderlyingCauseType, 'id'>,
  ): Promise<IncidentUnderlyingCauseType> {
    return this.incidentUnderlyingCauseRepository.incidentUnderlyingCauseTypes(id).create(incidentUnderlyingCauseType);
  }

  @patch('/incident-underlying-causes/{id}/incident-underlying-cause-types', {
    responses: {
      '200': {
        description: 'IncidentUnderlyingCause.IncidentUnderlyingCauseType PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(IncidentUnderlyingCauseType, {partial: true}),
        },
      },
    })
    incidentUnderlyingCauseType: Partial<IncidentUnderlyingCauseType>,
    @param.query.object('where', getWhereSchemaFor(IncidentUnderlyingCauseType)) where?: Where<IncidentUnderlyingCauseType>,
  ): Promise<Count> {
    return this.incidentUnderlyingCauseRepository.incidentUnderlyingCauseTypes(id).patch(incidentUnderlyingCauseType, where);
  }

  @del('/incident-underlying-causes/{id}/incident-underlying-cause-types', {
    responses: {
      '200': {
        description: 'IncidentUnderlyingCause.IncidentUnderlyingCauseType DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(IncidentUnderlyingCauseType)) where?: Where<IncidentUnderlyingCauseType>,
  ): Promise<Count> {
    return this.incidentUnderlyingCauseRepository.incidentUnderlyingCauseTypes(id).delete(where);
  }
}
