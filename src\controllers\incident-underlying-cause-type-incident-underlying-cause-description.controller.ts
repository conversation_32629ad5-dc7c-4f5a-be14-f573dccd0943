import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  IncidentUnderlyingCauseType,
  IncidentUnderlyingCauseDescription,
} from '../models';
import {IncidentUnderlyingCauseTypeRepository} from '../repositories';

export class IncidentUnderlyingCauseTypeIncidentUnderlyingCauseDescriptionController {
  constructor(
    @repository(IncidentUnderlyingCauseTypeRepository) protected incidentUnderlyingCauseTypeRepository: IncidentUnderlyingCauseTypeRepository,
  ) { }

  @get('/incident-underlying-cause-types/{id}/incident-underlying-cause-descriptions', {
    responses: {
      '200': {
        description: 'Array of IncidentUnderlyingCauseType has many IncidentUnderlyingCauseDescription',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(IncidentUnderlyingCauseDescription)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<IncidentUnderlyingCauseDescription>,
  ): Promise<IncidentUnderlyingCauseDescription[]> {
    return this.incidentUnderlyingCauseTypeRepository.incidentUnderlyingCauseDescriptions(id).find(filter);
  }

  @post('/incident-underlying-cause-types/{id}/incident-underlying-cause-descriptions', {
    responses: {
      '200': {
        description: 'IncidentUnderlyingCauseType model instance',
        content: {'application/json': {schema: getModelSchemaRef(IncidentUnderlyingCauseDescription)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof IncidentUnderlyingCauseType.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(IncidentUnderlyingCauseDescription, {
            title: 'NewIncidentUnderlyingCauseDescriptionInIncidentUnderlyingCauseType',
            exclude: ['id'],
            optional: ['incidentUnderlyingCauseTypeId']
          }),
        },
      },
    }) incidentUnderlyingCauseDescription: Omit<IncidentUnderlyingCauseDescription, 'id'>,
  ): Promise<IncidentUnderlyingCauseDescription> {
    return this.incidentUnderlyingCauseTypeRepository.incidentUnderlyingCauseDescriptions(id).create(incidentUnderlyingCauseDescription);
  }

  @patch('/incident-underlying-cause-types/{id}/incident-underlying-cause-descriptions', {
    responses: {
      '200': {
        description: 'IncidentUnderlyingCauseType.IncidentUnderlyingCauseDescription PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(IncidentUnderlyingCauseDescription, {partial: true}),
        },
      },
    })
    incidentUnderlyingCauseDescription: Partial<IncidentUnderlyingCauseDescription>,
    @param.query.object('where', getWhereSchemaFor(IncidentUnderlyingCauseDescription)) where?: Where<IncidentUnderlyingCauseDescription>,
  ): Promise<Count> {
    return this.incidentUnderlyingCauseTypeRepository.incidentUnderlyingCauseDescriptions(id).patch(incidentUnderlyingCauseDescription, where);
  }

  @del('/incident-underlying-cause-types/{id}/incident-underlying-cause-descriptions', {
    responses: {
      '200': {
        description: 'IncidentUnderlyingCauseType.IncidentUnderlyingCauseDescription DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(IncidentUnderlyingCauseDescription)) where?: Where<IncidentUnderlyingCauseDescription>,
  ): Promise<Count> {
    return this.incidentUnderlyingCauseTypeRepository.incidentUnderlyingCauseDescriptions(id).delete(where);
  }
}
