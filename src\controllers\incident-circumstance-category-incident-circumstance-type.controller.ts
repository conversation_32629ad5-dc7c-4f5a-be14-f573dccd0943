import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  IncidentCircumstanceCategory,
  IncidentCircumstanceType,
} from '../models';
import {IncidentCircumstanceCategoryRepository} from '../repositories';

export class IncidentCircumstanceCategoryIncidentCircumstanceTypeController {
  constructor(
    @repository(IncidentCircumstanceCategoryRepository) protected incidentCircumstanceCategoryRepository: IncidentCircumstanceCategoryRepository,
  ) { }

  @get('/incident-circumstance-categories/{id}/incident-circumstance-types', {
    responses: {
      '200': {
        description: 'Array of IncidentCircumstanceCategory has many IncidentCircumstanceType',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(IncidentCircumstanceType)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<IncidentCircumstanceType>,
  ): Promise<IncidentCircumstanceType[]> {
    return this.incidentCircumstanceCategoryRepository.incidentCircumstanceTypes(id).find(filter);
  }

  @post('/incident-circumstance-categories/{id}/incident-circumstance-types', {
    responses: {
      '200': {
        description: 'IncidentCircumstanceCategory model instance',
        content: {'application/json': {schema: getModelSchemaRef(IncidentCircumstanceType)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof IncidentCircumstanceCategory.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(IncidentCircumstanceType, {
            title: 'NewIncidentCircumstanceTypeInIncidentCircumstanceCategory',
            exclude: ['id'],
            optional: ['incidentCircumstanceCategoryId']
          }),
        },
      },
    }) incidentCircumstanceType: Omit<IncidentCircumstanceType, 'id'>,
  ): Promise<IncidentCircumstanceType> {
    return this.incidentCircumstanceCategoryRepository.incidentCircumstanceTypes(id).create(incidentCircumstanceType);
  }

  @patch('/incident-circumstance-categories/{id}/incident-circumstance-types', {
    responses: {
      '200': {
        description: 'IncidentCircumstanceCategory.IncidentCircumstanceType PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(IncidentCircumstanceType, {partial: true}),
        },
      },
    })
    incidentCircumstanceType: Partial<IncidentCircumstanceType>,
    @param.query.object('where', getWhereSchemaFor(IncidentCircumstanceType)) where?: Where<IncidentCircumstanceType>,
  ): Promise<Count> {
    return this.incidentCircumstanceCategoryRepository.incidentCircumstanceTypes(id).patch(incidentCircumstanceType, where);
  }

  @del('/incident-circumstance-categories/{id}/incident-circumstance-types', {
    responses: {
      '200': {
        description: 'IncidentCircumstanceCategory.IncidentCircumstanceType DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(IncidentCircumstanceType)) where?: Where<IncidentCircumstanceType>,
  ): Promise<Count> {
    return this.incidentCircumstanceCategoryRepository.incidentCircumstanceTypes(id).delete(where);
  }
}
