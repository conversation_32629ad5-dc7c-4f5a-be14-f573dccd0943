import {Entity, model, property, hasMany} from '@loopback/repository';
import {IncidentUnderlyingCauseDescription} from './incident-underlying-cause-description.model';

@model()
export class IncidentUnderlyingCauseType extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'string',
  })
  incidentUnderlyingCauseId?: string;

  @hasMany(() => IncidentUnderlyingCauseDescription)
  incidentUnderlyingCauseDescriptions: IncidentUnderlyingCauseDescription[];

  constructor(data?: Partial<IncidentUnderlyingCauseType>) {
    super(data);
  }
}

export interface IncidentUnderlyingCauseTypeRelations {
  // describe navigational properties here
}

export type IncidentUnderlyingCauseTypeWithRelations = IncidentUnderlyingCauseType & IncidentUnderlyingCauseTypeRelations;
