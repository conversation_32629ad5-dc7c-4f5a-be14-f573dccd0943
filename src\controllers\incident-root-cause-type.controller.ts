import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {IncidentRootCauseType} from '../models';
import {IncidentRootCauseTypeRepository} from '../repositories';

export class IncidentRootCauseTypeController {
  constructor(
    @repository(IncidentRootCauseTypeRepository)
    public incidentRootCauseTypeRepository : IncidentRootCauseTypeRepository,
  ) {}

  @post('/incident-root-cause-types')
  @response(200, {
    description: 'IncidentRootCauseType model instance',
    content: {'application/json': {schema: getModelSchemaRef(IncidentRootCauseType)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(IncidentRootCauseType, {
            title: 'NewIncidentRootCauseType',
            exclude: ['id'],
          }),
        },
      },
    })
    incidentRootCauseType: Omit<IncidentRootCauseType, 'id'>,
  ): Promise<IncidentRootCauseType> {
    return this.incidentRootCauseTypeRepository.create(incidentRootCauseType);
  }

  @get('/incident-root-cause-types/count')
  @response(200, {
    description: 'IncidentRootCauseType model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(IncidentRootCauseType) where?: Where<IncidentRootCauseType>,
  ): Promise<Count> {
    return this.incidentRootCauseTypeRepository.count(where);
  }

  @get('/incident-root-cause-types')
  @response(200, {
    description: 'Array of IncidentRootCauseType model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(IncidentRootCauseType, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(IncidentRootCauseType) filter?: Filter<IncidentRootCauseType>,
  ): Promise<IncidentRootCauseType[]> {
    return this.incidentRootCauseTypeRepository.find(filter);
  }

  @patch('/incident-root-cause-types')
  @response(200, {
    description: 'IncidentRootCauseType PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(IncidentRootCauseType, {partial: true}),
        },
      },
    })
    incidentRootCauseType: IncidentRootCauseType,
    @param.where(IncidentRootCauseType) where?: Where<IncidentRootCauseType>,
  ): Promise<Count> {
    return this.incidentRootCauseTypeRepository.updateAll(incidentRootCauseType, where);
  }

  @get('/incident-root-cause-types/{id}')
  @response(200, {
    description: 'IncidentRootCauseType model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(IncidentRootCauseType, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(IncidentRootCauseType, {exclude: 'where'}) filter?: FilterExcludingWhere<IncidentRootCauseType>
  ): Promise<IncidentRootCauseType> {
    return this.incidentRootCauseTypeRepository.findById(id, filter);
  }

  @patch('/incident-root-cause-types/{id}')
  @response(204, {
    description: 'IncidentRootCauseType PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(IncidentRootCauseType, {partial: true}),
        },
      },
    })
    incidentRootCauseType: IncidentRootCauseType,
  ): Promise<void> {
    await this.incidentRootCauseTypeRepository.updateById(id, incidentRootCauseType);
  }

  @put('/incident-root-cause-types/{id}')
  @response(204, {
    description: 'IncidentRootCauseType PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() incidentRootCauseType: IncidentRootCauseType,
  ): Promise<void> {
    await this.incidentRootCauseTypeRepository.replaceById(id, incidentRootCauseType);
  }

  @del('/incident-root-cause-types/{id}')
  @response(204, {
    description: 'IncidentRootCauseType DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.incidentRootCauseTypeRepository.deleteById(id);
  }
}
