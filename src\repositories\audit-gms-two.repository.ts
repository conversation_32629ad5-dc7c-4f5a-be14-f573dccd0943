import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {AuditGmsTwo, AuditGmsTwoRelations, AuditGmsThree} from '../models';
import {AuditGmsThreeRepository} from './audit-gms-three.repository';

export class AuditGmsTwoRepository extends DefaultCrudRepository<
  AuditGmsTwo,
  typeof AuditGmsTwo.prototype.id,
  AuditGmsTwoRelations
> {

  public readonly auditGmsThrees: HasManyRepositoryFactory<AuditGmsThree, typeof AuditGmsTwo.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('AuditGmsThreeRepository') protected auditGmsThreeRepositoryGetter: Getter<AuditGmsThreeRepository>,
  ) {
    super(AuditGmsTwo, dataSource);
    this.auditGmsThrees = this.createHasManyRepositoryFactoryFor('auditGmsThrees', auditGmsThreeRepositoryGetter,);
    this.registerInclusionResolver('auditGmsThrees', this.auditGmsThrees.inclusionResolver);
  }
}
