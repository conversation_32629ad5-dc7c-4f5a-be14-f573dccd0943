import { Entity, model, property, hasMany} from '@loopback/repository';
import {LocationThree} from './location-three.model';
import {UserLocationRole} from './user-location-role.model';

@model()
export class LocationTwo extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'number',
  })
  order?: number;
  
  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'string',
  })
  modified?: string;

  @property({
    type: 'string',
  })
  locationOneId?: string;

  @hasMany(() => LocationThree)
  locationThrees: LocationThree[];

  @hasMany(() => UserLocationRole)
  userLocationRoles: UserLocationRole[];

  constructor(data?: Partial<LocationTwo>) {
    super(data);
  }
}

export interface LocationTwoRelations {
  // describe navigational properties here
}

export type LocationTwoWithRelations = LocationTwo & LocationTwoRelations;
