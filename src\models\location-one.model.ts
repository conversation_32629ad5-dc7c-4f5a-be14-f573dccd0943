import {Entity, model, property, hasMany} from '@loopback/repository';
import {LocationTwo} from './location-two.model';
import {UserLocationRole} from './user-location-role.model';

@model()
export class LocationOne extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'number',
  })
  order?: number;
  
  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'string',
  })
  modified?: string;

  @hasMany(() => LocationTwo)
  locationTwos: LocationTwo[];

  @hasMany(() => UserLocationRole)
  userLocationRoles: UserLocationRole[];

  constructor(data?: Partial<LocationOne>) {
    super(data);
  }
}

export interface LocationOneRelations {
  // describe navigational properties here
}

export type LocationOneWithRelations = LocationOne & LocationOneRelations;
