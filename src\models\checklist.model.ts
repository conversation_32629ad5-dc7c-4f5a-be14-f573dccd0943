import {Entity, model, property} from '@loopback/repository';

@model()
export class Checklist extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'string',
  })
  application?: string;

  @property({
    type: 'string',
  })
  version?: string;

  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'string',
  })
  modified?: string;

  @property({
    type: 'any',
  })
  value?: any;


  constructor(data?: Partial<Checklist>) {
    super(data);
  }
}

export interface ChecklistRelations {
  // describe navigational properties here
}

export type ChecklistWithRelations = Checklist & ChecklistRelations;
