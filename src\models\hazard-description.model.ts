import {Entity, model, property, hasMany} from '@loopback/repository';
import {HazardGms} from './hazard-gms.model';

@model()
export class HazardDescription extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'string',
  })
  hazardTypeId?: string;

  @hasMany(() => HazardGms)
  hazardGms: HazardGms[];

  constructor(data?: Partial<HazardDescription>) {
    super(data);
  }
}

export interface HazardDescriptionRelations {
  // describe navigational properties here
}

export type HazardDescriptionWithRelations = HazardDescription & HazardDescriptionRelations;
