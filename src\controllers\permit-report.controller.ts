import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import { PermitReport } from '../models';
import { PermitReportRepository, ActionRepository } from '../repositories';

import { authenticate } from '@loopback/authentication';


@authenticate('jwt')
export class PermitReportController {
  constructor(
    @repository(PermitReportRepository)
    public permitReportRepository: PermitReportRepository,
    @repository(ActionRepository)
    public actionRepository: ActionRepository,
  ) { }

  @post('/permit-reports')
  @response(200, {
    description: 'PermitReport model instance',
    content: { 'application/json': { schema: getModelSchemaRef(PermitReport) } },
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, {
            title: 'NewPermitReport',
            exclude: ['id'],
          }),
        },
      },
    })
    permitReport: Omit<PermitReport, 'id'>,
  ): Promise<PermitReport> {
    return this.permitReportRepository.create(permitReport);
  }

  @get('/permit-reports/count')
  @response(200, {
    description: 'PermitReport model count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async count(
    @param.where(PermitReport) where?: Where<PermitReport>,
  ): Promise<Count> {
    return this.permitReportRepository.count(where);
  }

  @get('/permit-reports')
  @response(200, {
    description: 'Array of PermitReport model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(PermitReport, { includeRelations: true }),
        },
      },
    },
  })
  async find(
    @param.filter(PermitReport) filter?: Filter<PermitReport>,
  ): Promise<PermitReport[]> {
    return this.permitReportRepository.find(filter);
  }

  @patch('/permit-reports')
  @response(200, {
    description: 'PermitReport PATCH success count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, { partial: true }),
        },
      },
    })
    permitReport: PermitReport,
    @param.where(PermitReport) where?: Where<PermitReport>,
  ): Promise<Count> {
    return this.permitReportRepository.updateAll(permitReport, where);
  }

  @get('/permit-reports/{id}')
  @response(200, {
    description: 'PermitReport model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(PermitReport, { includeRelations: true }),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(PermitReport, { exclude: 'where' }) filter?: FilterExcludingWhere<PermitReport>
  ): Promise<PermitReport> {
    return this.permitReportRepository.findById(id, filter);
  }

  @patch('/permit-reports/{id}')
  @response(204, {
    description: 'PermitReport PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, { partial: true }),
        },
      },
    })
    permitReport: PermitReport,
  ): Promise<void> {
    await this.permitReportRepository.updateById(id, permitReport);
  }

  @put('/permit-reports/{id}')
  @response(204, {
    description: 'PermitReport PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() permitReport: PermitReport,
  ): Promise<void> {
    await this.permitReportRepository.replaceById(id, permitReport);
  }

  @del('/permit-reports/{id}')
  @response(204, {
    description: 'PermitReport DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.permitReportRepository.deleteById(id);
    await this.actionRepository.deleteAll({ objectId: id })
  }
}
