import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  Inspection,
  LocationOne,
} from '../models';
import {InspectionRepository} from '../repositories';

export class InspectionLocationOneController {
  constructor(
    @repository(InspectionRepository)
    public inspectionRepository: InspectionRepository,
  ) { }

  @get('/inspections/{id}/location-one', {
    responses: {
      '200': {
        description: 'LocationOne belonging to Inspection',
        content: {
          'application/json': {
            schema: getModelSchemaRef(LocationOne),
          },
        },
      },
    },
  })
  async getLocationOne(
    @param.path.string('id') id: typeof Inspection.prototype.id,
  ): Promise<LocationOne> {
    return this.inspectionRepository.locationOne(id);
  }
}
