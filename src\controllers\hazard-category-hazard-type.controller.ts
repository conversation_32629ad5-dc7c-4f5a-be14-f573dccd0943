import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  HazardCategory,
  HazardType,
} from '../models';
import {HazardCategoryRepository} from '../repositories';

export class HazardCategoryHazardTypeController {
  constructor(
    @repository(HazardCategoryRepository) protected hazardCategoryRepository: HazardCategoryRepository,
  ) { }

  @get('/hazard-categories/{id}/hazard-types', {
    responses: {
      '200': {
        description: 'Array of HazardCategory has many HazardType',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(HazardType)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<HazardType>,
  ): Promise<HazardType[]> {
    return this.hazardCategoryRepository.hazardTypes(id).find(filter);
  }

  @post('/hazard-categories/{id}/hazard-types', {
    responses: {
      '200': {
        description: 'HazardCategory model instance',
        content: {'application/json': {schema: getModelSchemaRef(HazardType)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof HazardCategory.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(HazardType, {
            title: 'NewHazardTypeInHazardCategory',
            exclude: ['id'],
            optional: ['hazardCategoryId']
          }),
        },
      },
    }) hazardType: Omit<HazardType, 'id'>,
  ): Promise<HazardType> {
    return this.hazardCategoryRepository.hazardTypes(id).create(hazardType);
  }

  @patch('/hazard-categories/{id}/hazard-types', {
    responses: {
      '200': {
        description: 'HazardCategory.HazardType PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(HazardType, {partial: true}),
        },
      },
    })
    hazardType: Partial<HazardType>,
    @param.query.object('where', getWhereSchemaFor(HazardType)) where?: Where<HazardType>,
  ): Promise<Count> {
    return this.hazardCategoryRepository.hazardTypes(id).patch(hazardType, where);
  }

  @del('/hazard-categories/{id}/hazard-types', {
    responses: {
      '200': {
        description: 'HazardCategory.HazardType DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(HazardType)) where?: Where<HazardType>,
  ): Promise<Count> {
    return this.hazardCategoryRepository.hazardTypes(id).delete(where);
  }
}
