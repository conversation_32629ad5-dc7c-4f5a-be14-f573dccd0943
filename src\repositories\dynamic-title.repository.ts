import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {DynamicTitle, DynamicTitleRelations} from '../models';

export class DynamicTitleRepository extends DefaultCrudRepository<
  DynamicTitle,
  typeof DynamicTitle.prototype.id,
  DynamicTitleRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(DynamicTitle, dataSource);
  }
}
