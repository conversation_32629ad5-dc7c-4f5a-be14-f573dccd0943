import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {IncidentRole} from '../models';
import {IncidentRoleRepository} from '../repositories';
import { authenticate } from '@loopback/authentication';
@authenticate('jwt')
export class IncidentRoleController {
  constructor(
    @repository(IncidentRoleRepository)
    public incidentRoleRepository : IncidentRoleRepository,
  ) {}

  @post('/incident-roles')
  @response(200, {
    description: 'IncidentRole model instance',
    content: {'application/json': {schema: getModelSchemaRef(IncidentRole)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(IncidentRole, {
            title: 'NewIncidentRole',
            exclude: ['id'],
          }),
        },
      },
    })
    incidentRole: Omit<IncidentRole, 'id'>,
  ): Promise<IncidentRole> {
    return this.incidentRoleRepository.create(incidentRole);
  }

  @get('/incident-roles/count')
  @response(200, {
    description: 'IncidentRole model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(IncidentRole) where?: Where<IncidentRole>,
  ): Promise<Count> {
    return this.incidentRoleRepository.count(where);
  }

  @get('/incident-roles')
  @response(200, {
    description: 'Array of IncidentRole model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(IncidentRole, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(IncidentRole) filter?: Filter<IncidentRole>,
  ): Promise<IncidentRole[]> {
    return this.incidentRoleRepository.find(filter);
  }

  @patch('/incident-roles')
  @response(200, {
    description: 'IncidentRole PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(IncidentRole, {partial: true}),
        },
      },
    })
    incidentRole: IncidentRole,
    @param.where(IncidentRole) where?: Where<IncidentRole>,
  ): Promise<Count> {
    return this.incidentRoleRepository.updateAll(incidentRole, where);
  }

  @get('/incident-roles/{id}')
  @response(200, {
    description: 'IncidentRole model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(IncidentRole, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(IncidentRole, {exclude: 'where'}) filter?: FilterExcludingWhere<IncidentRole>
  ): Promise<IncidentRole> {
    return this.incidentRoleRepository.findById(id, filter);
  }

  @patch('/incident-roles/{id}')
  @response(204, {
    description: 'IncidentRole PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(IncidentRole, {partial: true}),
        },
      },
    })
    incidentRole: IncidentRole,
  ): Promise<void> {
    await this.incidentRoleRepository.updateById(id, incidentRole);
  }

  @put('/incident-roles/{id}')
  @response(204, {
    description: 'IncidentRole PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() incidentRole: IncidentRole,
  ): Promise<void> {
    await this.incidentRoleRepository.replaceById(id, incidentRole);
  }

  @del('/incident-roles/{id}')
  @response(204, {
    description: 'IncidentRole DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.incidentRoleRepository.deleteById(id);
  }
}
