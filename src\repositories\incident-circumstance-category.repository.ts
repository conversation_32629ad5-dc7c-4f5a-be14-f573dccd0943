import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {IncidentCircumstanceCategory, IncidentCircumstanceCategoryRelations, IncidentCircumstanceType} from '../models';
import {IncidentCircumstanceTypeRepository} from './incident-circumstance-type.repository';

export class IncidentCircumstanceCategoryRepository extends DefaultCrudRepository<
  IncidentCircumstanceCategory,
  typeof IncidentCircumstanceCategory.prototype.id,
  IncidentCircumstanceCategoryRelations
> {

  public readonly incidentCircumstanceTypes: HasManyRepositoryFactory<IncidentCircumstanceType, typeof IncidentCircumstanceCategory.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('IncidentCircumstanceTypeRepository') protected incidentCircumstanceTypeRepositoryGetter: Getter<IncidentCircumstanceTypeRepository>,
  ) {
    super(IncidentCircumstanceCategory, dataSource);
    this.incidentCircumstanceTypes = this.createHasManyRepositoryFactoryFor('incidentCircumstanceTypes', incidentCircumstanceTypeRepositoryGetter,);
    this.registerInclusionResolver('incidentCircumstanceTypes', this.incidentCircumstanceTypes.inclusionResolver);
  }
}
