import {Entity, model, property} from '@loopback/repository';

@model()
export class Sample extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'array',
    itemType: 'object',
  })
  arrayOfObject?: object[];

  @property({
    type: 'any',
  })
  any?: any;

  @property({
    type: 'array',
    itemType: 'any',
  })
  arrayofAny?: any[];


  constructor(data?: Partial<Sample>) {
    super(data);
  }
}

export interface SampleRelations {
  // describe navigational properties here
}

export type SampleWithRelations = Sample & SampleRelations;
