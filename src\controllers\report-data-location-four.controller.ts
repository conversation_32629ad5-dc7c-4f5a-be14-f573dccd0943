import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ReportData,
  LocationFour,
} from '../models';
import {ReportDataRepository} from '../repositories';

export class ReportDataLocationFourController {
  constructor(
    @repository(ReportDataRepository)
    public reportDataRepository: ReportDataRepository,
  ) { }

  @get('/report-data/{id}/location-four', {
    responses: {
      '200': {
        description: 'LocationFour belonging to ReportData',
        content: {
          'application/json': {
            schema: getModelSchemaRef(LocationFour),
          },
        },
      },
    },
  })
  async getLocationFour(
    @param.path.string('id') id: typeof ReportData.prototype.id,
  ): Promise<LocationFour> {
    return this.reportDataRepository.locationFour(id);
  }
}
