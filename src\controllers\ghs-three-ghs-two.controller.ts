import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  GhsThree,
  GhsTwo,
} from '../models';
import {GhsThreeRepository} from '../repositories';

export class GhsThreeGhsTwoController {
  constructor(
    @repository(GhsThreeRepository)
    public ghsThreeRepository: GhsThreeRepository,
  ) { }

  @get('/ghs-threes/{id}/ghs-two', {
    responses: {
      '200': {
        description: 'GhsTwo belonging to GhsThree',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(GhsTwo)},
          },
        },
      },
    },
  })
  async getGhsTwo(
    @param.path.string('id') id: typeof GhsThree.prototype.id,
  ): Promise<GhsTwo> {
    return this.ghsThreeRepository.ghsTwo(id);
  }
}
