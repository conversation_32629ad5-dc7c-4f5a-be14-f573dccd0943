import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {IncidentCircumstanceDescription, IncidentCircumstanceDescriptionRelations} from '../models';

export class IncidentCircumstanceDescriptionRepository extends DefaultCrudRepository<
  IncidentCircumstanceDescription,
  typeof IncidentCircumstanceDescription.prototype.id,
  IncidentCircumstanceDescriptionRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(IncidentCircumstanceDescription, dataSource);
  }
}
