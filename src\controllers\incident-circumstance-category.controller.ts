import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {IncidentCircumstanceCategory} from '../models';
import {IncidentCircumstanceCategoryRepository} from '../repositories';

export class IncidentCircumstanceCategoryController {
  constructor(
    @repository(IncidentCircumstanceCategoryRepository)
    public incidentCircumstanceCategoryRepository : IncidentCircumstanceCategoryRepository,
  ) {}

  @post('/incident-circumstance-categories')
  @response(200, {
    description: 'IncidentCircumstanceCategory model instance',
    content: {'application/json': {schema: getModelSchemaRef(IncidentCircumstanceCategory)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(IncidentCircumstanceCategory, {
            title: 'NewIncidentCircumstanceCategory',
            exclude: ['id'],
          }),
        },
      },
    })
    incidentCircumstanceCategory: Omit<IncidentCircumstanceCategory, 'id'>,
  ): Promise<IncidentCircumstanceCategory> {
    return this.incidentCircumstanceCategoryRepository.create(incidentCircumstanceCategory);
  }

  @get('/incident-circumstance-categories/count')
  @response(200, {
    description: 'IncidentCircumstanceCategory model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(IncidentCircumstanceCategory) where?: Where<IncidentCircumstanceCategory>,
  ): Promise<Count> {
    return this.incidentCircumstanceCategoryRepository.count(where);
  }

  @get('/incident-circumstance-categories')
  @response(200, {
    description: 'Array of IncidentCircumstanceCategory model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(IncidentCircumstanceCategory, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(IncidentCircumstanceCategory) filter?: Filter<IncidentCircumstanceCategory>,
  ): Promise<IncidentCircumstanceCategory[]> {
    return this.incidentCircumstanceCategoryRepository.find(filter);
  }

  @patch('/incident-circumstance-categories')
  @response(200, {
    description: 'IncidentCircumstanceCategory PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(IncidentCircumstanceCategory, {partial: true}),
        },
      },
    })
    incidentCircumstanceCategory: IncidentCircumstanceCategory,
    @param.where(IncidentCircumstanceCategory) where?: Where<IncidentCircumstanceCategory>,
  ): Promise<Count> {
    return this.incidentCircumstanceCategoryRepository.updateAll(incidentCircumstanceCategory, where);
  }

  @get('/incident-circumstance-categories/{id}')
  @response(200, {
    description: 'IncidentCircumstanceCategory model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(IncidentCircumstanceCategory, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(IncidentCircumstanceCategory, {exclude: 'where'}) filter?: FilterExcludingWhere<IncidentCircumstanceCategory>
  ): Promise<IncidentCircumstanceCategory> {
    return this.incidentCircumstanceCategoryRepository.findById(id, filter);
  }

  @patch('/incident-circumstance-categories/{id}')
  @response(204, {
    description: 'IncidentCircumstanceCategory PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(IncidentCircumstanceCategory, {partial: true}),
        },
      },
    })
    incidentCircumstanceCategory: IncidentCircumstanceCategory,
  ): Promise<void> {
    await this.incidentCircumstanceCategoryRepository.updateById(id, incidentCircumstanceCategory);
  }

  @put('/incident-circumstance-categories/{id}')
  @response(204, {
    description: 'IncidentCircumstanceCategory PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() incidentCircumstanceCategory: IncidentCircumstanceCategory,
  ): Promise<void> {
    await this.incidentCircumstanceCategoryRepository.replaceById(id, incidentCircumstanceCategory);
  }

  @del('/incident-circumstance-categories/{id}')
  @response(204, {
    description: 'IncidentCircumstanceCategory DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.incidentCircumstanceCategoryRepository.deleteById(id);
  }
}
