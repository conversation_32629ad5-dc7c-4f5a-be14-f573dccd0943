import {Entity, model, property} from '@loopback/repository';

@model()
export class SurfaceCondition extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  description?: string;


  constructor(data?: Partial<SurfaceCondition>) {
    super(data);
  }
}

export interface SurfaceConditionRelations {
  // describe navigational properties here
}

export type SurfaceConditionWithRelations = SurfaceCondition & SurfaceConditionRelations;
