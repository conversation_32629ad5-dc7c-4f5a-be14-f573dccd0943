import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  PermitReport,
  User,
} from '../models';
import {PermitReportRepository} from '../repositories';

export class PermitReportUserController {
  constructor(
    @repository(PermitReportRepository)
    public permitReportRepository: PermitReportRepository,
  ) { }

  @get('/permit-reports/{id}/user', {
    responses: {
      '200': {
        description: 'User belonging to PermitReport',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(User)},
          },
        },
      },
    },
  })
  async getUser(
    @param.path.string('id') id: typeof PermitReport.prototype.id,
  ): Promise<User> {
    return this.permitReportRepository.applicant(id);
  }
}
