import { Entity, model, property, hasMany} from '@loopback/repository';
import {AuditGmsThree} from './audit-gms-three.model';

@model()
export class AuditGmsTwo extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'number',
  })
  order?: number;

  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'string',
  })
  modified?: string;

  @property({
    type: 'string',
  })
  auditGmsOneId?: string;

  @hasMany(() => AuditGmsThree)
  auditGmsThrees: AuditGmsThree[];

  constructor(data?: Partial<AuditGmsTwo>) {
    super(data);
  }
}

export interface AuditGmsTwoRelations {
  // describe navigational properties here
}

export type AuditGmsTwoWithRelations = AuditGmsTwo & AuditGmsTwoRelations;
