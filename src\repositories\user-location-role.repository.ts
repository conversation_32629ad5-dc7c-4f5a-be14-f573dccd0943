import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {UserLocationRole, UserLocationRoleRelations} from '../models';

export class UserLocationRoleRepository extends DefaultCrudRepository<
  UserLocationRole,
  typeof UserLocationRole.prototype.id,
  UserLocationRoleRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(UserLocationRole, dataSource);
  }
}
