import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {HumanTwo} from '../models';
import {HumanTwoRepository} from '../repositories';

export class HumanTwoController {
  constructor(
    @repository(HumanTwoRepository)
    public humanTwoRepository : HumanTwoRepository,
  ) {}

  @post('/human-twos')
  @response(200, {
    description: 'HumanTwo model instance',
    content: {'application/json': {schema: getModelSchemaRef(HumanTwo)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(HumanTwo, {
            title: 'NewHumanTwo',
            exclude: ['id'],
          }),
        },
      },
    })
    humanTwo: Omit<HumanTwo, 'id'>,
  ): Promise<HumanTwo> {
    return this.humanTwoRepository.create(humanTwo);
  }

  @get('/human-twos/count')
  @response(200, {
    description: 'HumanTwo model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(HumanTwo) where?: Where<HumanTwo>,
  ): Promise<Count> {
    return this.humanTwoRepository.count(where);
  }

  @get('/human-twos')
  @response(200, {
    description: 'Array of HumanTwo model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(HumanTwo, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(HumanTwo) filter?: Filter<HumanTwo>,
  ): Promise<HumanTwo[]> {
    return this.humanTwoRepository.find(filter);
  }

  @patch('/human-twos')
  @response(200, {
    description: 'HumanTwo PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(HumanTwo, {partial: true}),
        },
      },
    })
    humanTwo: HumanTwo,
    @param.where(HumanTwo) where?: Where<HumanTwo>,
  ): Promise<Count> {
    return this.humanTwoRepository.updateAll(humanTwo, where);
  }

  @get('/human-twos/{id}')
  @response(200, {
    description: 'HumanTwo model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(HumanTwo, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(HumanTwo, {exclude: 'where'}) filter?: FilterExcludingWhere<HumanTwo>
  ): Promise<HumanTwo> {
    return this.humanTwoRepository.findById(id, filter);
  }

  @patch('/human-twos/{id}')
  @response(204, {
    description: 'HumanTwo PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(HumanTwo, {partial: true}),
        },
      },
    })
    humanTwo: HumanTwo,
  ): Promise<void> {
    await this.humanTwoRepository.updateById(id, humanTwo);
  }

  @put('/human-twos/{id}')
  @response(204, {
    description: 'HumanTwo PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() humanTwo: HumanTwo,
  ): Promise<void> {
    await this.humanTwoRepository.replaceById(id, humanTwo);
  }

  @del('/human-twos/{id}')
  @response(204, {
    description: 'HumanTwo DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.humanTwoRepository.deleteById(id);
  }
}
