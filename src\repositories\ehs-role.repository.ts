import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {EhsRole, EhsRoleRelations} from '../models';

export class EhsRoleRepository extends DefaultCrudRepository<
  EhsRole,
  typeof EhsRole.prototype.id,
  EhsRoleRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(EhsRole, dataSource);
  }
}
