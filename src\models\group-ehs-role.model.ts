import { Entity, model, property } from '@loopback/repository';

@model()
export class GroupEhsRole extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'any',
  })
  permissions?: any;

  @property({
    type: 'number',
  })
  order?: number;

  @property({
    type: 'date',
  })
  created?: string;

  constructor(data?: Partial<GroupEhsRole>) {
    super(data);
  }
}

export interface GroupEhsRoleRelations {
  // describe navigational properties here
}

export type GroupEhsRoleWithRelations = GroupEhsRole & GroupEhsRoleRelations;
