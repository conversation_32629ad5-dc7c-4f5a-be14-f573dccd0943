import {Entity, model, property, belongsTo} from '@loopback/repository';
import {User} from './user.model';
import {Checklist} from './checklist.model';
import {LocationOne} from './location-one.model';
import {LocationTwo} from './location-two.model';
import {LocationThree} from './location-three.model';
import {LocationFour} from './location-four.model';

@model()
export class Inspection extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  month?: string;

  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'string',
  })
  maskId?: string;

  @property({
    type: 'string',
  })
  dateTime?: string;

  @property({
    type: 'string',
  })
  status?: string;

  @property({
    type: 'string',
  })
  year?: string;

  @property({
    type: 'any',
  })
  checklistReport?: any;

  @property({
    type: 'any',
  })
  inspectionData?: any;

  @property({
    type: 'any',

  })
  postActions?: any;

  @property({
    type: 'string',
  })
  remarks?: string;

  @belongsTo(() => User)
  approverId: string;

  @belongsTo(() => User)
  assignedById: string;

  @belongsTo(() => Checklist)
  checklistId: string;

  @belongsTo(() => User)
  assignedToId: string;

  @belongsTo(() => LocationOne)
  locationOneId: string;

  @belongsTo(() => LocationTwo)
  locationTwoId: string;

  @belongsTo(() => LocationThree)
  locationThreeId: string;

  @belongsTo(() => LocationFour)
  locationFourId: string;

  constructor(data?: Partial<Inspection>) {
    super(data);
  }
}

export interface InspectionRelations {
  // describe navigational properties here
}

export type InspectionWithRelations = Inspection & InspectionRelations;
