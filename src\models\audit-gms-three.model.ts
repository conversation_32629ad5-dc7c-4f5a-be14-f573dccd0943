import { Entity, model, property } from '@loopback/repository';

@model()
export class AuditGmsThree extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;
  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'number',
  })
  order?: number;

  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'string',
  })
  modified?: string;

  @property({
    type: 'string',
  })
  auditGmsTwoId?: string;

  constructor(data?: Partial<AuditGmsThree>) {
    super(data);
  }
}

export interface AuditGmsThreeRelations {
  // describe navigational properties here
}

export type AuditGmsThreeWithRelations = AuditGmsThree & AuditGmsThreeRelations;
