import {Entity, model, property, hasMany} from '@loopback/repository';
import {IncidentCircumstanceDescription} from './incident-circumstance-description.model';

@model()
export class IncidentCircumstanceType extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'string',
  })
  incidentCircumstanceCategoryId?: string;

  @hasMany(() => IncidentCircumstanceDescription)
  incidentCircumstanceDescriptions: IncidentCircumstanceDescription[];

  constructor(data?: Partial<IncidentCircumstanceType>) {
    super(data);
  }
}

export interface IncidentCircumstanceTypeRelations {
  // describe navigational properties here
}

export type IncidentCircumstanceTypeWithRelations = IncidentCircumstanceType & IncidentCircumstanceTypeRelations;
