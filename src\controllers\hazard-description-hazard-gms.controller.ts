import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  HazardDescription,
  HazardGms,
} from '../models';
import {HazardDescriptionRepository} from '../repositories';

export class HazardDescriptionHazardGmsController {
  constructor(
    @repository(HazardDescriptionRepository) protected hazardDescriptionRepository: HazardDescriptionRepository,
  ) { }

  @get('/hazard-descriptions/{id}/hazard-gms', {
    responses: {
      '200': {
        description: 'Array of HazardDescription has many HazardGms',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(HazardGms)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<HazardGms>,
  ): Promise<HazardGms[]> {
    return this.hazardDescriptionRepository.hazardGms(id).find(filter);
  }

  @post('/hazard-descriptions/{id}/hazard-gms', {
    responses: {
      '200': {
        description: 'HazardDescription model instance',
        content: {'application/json': {schema: getModelSchemaRef(HazardGms)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof HazardDescription.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(HazardGms, {
            title: 'NewHazardGmsInHazardDescription',
            exclude: ['id'],
            optional: ['hazardDescriptionId']
          }),
        },
      },
    }) hazardGms: Omit<HazardGms, 'id'>,
  ): Promise<HazardGms> {
    return this.hazardDescriptionRepository.hazardGms(id).create(hazardGms);
  }

  @patch('/hazard-descriptions/{id}/hazard-gms', {
    responses: {
      '200': {
        description: 'HazardDescription.HazardGms PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(HazardGms, {partial: true}),
        },
      },
    })
    hazardGms: Partial<HazardGms>,
    @param.query.object('where', getWhereSchemaFor(HazardGms)) where?: Where<HazardGms>,
  ): Promise<Count> {
    return this.hazardDescriptionRepository.hazardGms(id).patch(hazardGms, where);
  }

  @del('/hazard-descriptions/{id}/hazard-gms', {
    responses: {
      '200': {
        description: 'HazardDescription.HazardGms DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(HazardGms)) where?: Where<HazardGms>,
  ): Promise<Count> {
    return this.hazardDescriptionRepository.hazardGms(id).delete(where);
  }
}
