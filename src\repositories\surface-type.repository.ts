import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {SurfaceType, SurfaceTypeRelations} from '../models';

export class SurfaceTypeRepository extends DefaultCrudRepository<
  SurfaceType,
  typeof SurfaceType.prototype.id,
  SurfaceTypeRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(SurfaceType, dataSource);
  }
}
