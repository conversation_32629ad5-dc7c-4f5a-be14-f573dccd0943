import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  LocationOne,
  UserLocationRole,
} from '../models';
import {LocationOneRepository} from '../repositories';

export class LocationOneUserLocationRoleController {
  constructor(
    @repository(LocationOneRepository) protected locationOneRepository: LocationOneRepository,
  ) { }

  @get('/location-ones/{id}/user-location-roles', {
    responses: {
      '200': {
        description: 'Array of LocationOne has many UserLocationRole',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(UserLocationRole)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<UserLocationRole>,
  ): Promise<UserLocationRole[]> {
    return this.locationOneRepository.userLocationRoles(id).find(filter);
  }

  @post('/location-ones/{id}/user-location-roles', {
    responses: {
      '200': {
        description: 'LocationOne model instance',
        content: {'application/json': {schema: getModelSchemaRef(UserLocationRole)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof LocationOne.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(UserLocationRole, {
            title: 'NewUserLocationRoleInLocationOne',
            exclude: ['id'],
            optional: ['locationOneId']
          }),
        },
      },
    }) userLocationRole: Omit<UserLocationRole, 'id'>,
  ): Promise<UserLocationRole> {
    return this.locationOneRepository.userLocationRoles(id).create(userLocationRole);
  }

  @patch('/location-ones/{id}/user-location-roles', {
    responses: {
      '200': {
        description: 'LocationOne.UserLocationRole PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(UserLocationRole, {partial: true}),
        },
      },
    })
    userLocationRole: Partial<UserLocationRole>,
    @param.query.object('where', getWhereSchemaFor(UserLocationRole)) where?: Where<UserLocationRole>,
  ): Promise<Count> {
    return this.locationOneRepository.userLocationRoles(id).patch(userLocationRole, where);
  }

  @del('/location-ones/{id}/user-location-roles', {
    responses: {
      '200': {
        description: 'LocationOne.UserLocationRole DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(UserLocationRole)) where?: Where<UserLocationRole>,
  ): Promise<Count> {
    return this.locationOneRepository.userLocationRoles(id).delete(where);
  }
}
