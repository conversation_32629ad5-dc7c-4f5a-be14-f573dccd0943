import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  GhsTwo,
  GhsThree,
} from '../models';
import {GhsTwoRepository} from '../repositories';

export class GhsTwoGhsThreeController {
  constructor(
    @repository(GhsTwoRepository) protected ghsTwoRepository: GhsTwoRepository,
  ) { }

  @get('/ghs-twos/{id}/ghs-threes', {
    responses: {
      '200': {
        description: 'Array of GhsTwo has many GhsThree',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(GhsThree)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<GhsThree>,
  ): Promise<GhsThree[]> {
    return this.ghsTwoRepository.ghsThrees(id).find(filter);
  }

  @post('/ghs-twos/{id}/ghs-threes', {
    responses: {
      '200': {
        description: 'GhsTwo model instance',
        content: {'application/json': {schema: getModelSchemaRef(GhsThree)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof GhsTwo.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GhsThree, {
            title: 'NewGhsThreeInGhsTwo',
            exclude: ['id'],
            optional: ['ghsTwoId']
          }),
        },
      },
    }) ghsThree: Omit<GhsThree, 'id'>,
  ): Promise<GhsThree> {
    return this.ghsTwoRepository.ghsThrees(id).create(ghsThree);
  }

  @patch('/ghs-twos/{id}/ghs-threes', {
    responses: {
      '200': {
        description: 'GhsTwo.GhsThree PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GhsThree, {partial: true}),
        },
      },
    })
    ghsThree: Partial<GhsThree>,
    @param.query.object('where', getWhereSchemaFor(GhsThree)) where?: Where<GhsThree>,
  ): Promise<Count> {
    return this.ghsTwoRepository.ghsThrees(id).patch(ghsThree, where);
  }

  @del('/ghs-twos/{id}/ghs-threes', {
    responses: {
      '200': {
        description: 'GhsTwo.GhsThree DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(GhsThree)) where?: Where<GhsThree>,
  ): Promise<Count> {
    return this.ghsTwoRepository.ghsThrees(id).delete(where);
  }
}
