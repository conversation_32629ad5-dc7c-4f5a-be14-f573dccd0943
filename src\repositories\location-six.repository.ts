import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {LocationSix, LocationSixRelations} from '../models';

export class LocationSixRepository extends DefaultCrudRepository<
  LocationSix,
  typeof LocationSix.prototype.id,
  LocationSixRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(LocationSix, dataSource);
  }
}
