import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {WeatherCondition, WeatherConditionRelations} from '../models';

export class WeatherConditionRepository extends DefaultCrudRepository<
  WeatherCondition,
  typeof WeatherCondition.prototype.id,
  WeatherConditionRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(WeatherCondition, dataSource);
  }
}
