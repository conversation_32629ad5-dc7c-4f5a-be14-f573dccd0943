import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {OtherRole} from '../models';
import {OtherRoleRepository} from '../repositories';

export class OtherRoleController {
  constructor(
    @repository(OtherRoleRepository)
    public otherRoleRepository : OtherRoleRepository,
  ) {}

  @post('/other-roles')
  @response(200, {
    description: 'OtherRole model instance',
    content: {'application/json': {schema: getModelSchemaRef(OtherRole)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(OtherRole, {
            title: 'NewOtherRole',
            exclude: ['id'],
          }),
        },
      },
    })
    otherRole: Omit<OtherRole, 'id'>,
  ): Promise<OtherRole> {
    return this.otherRoleRepository.create(otherRole);
  }

  @get('/other-roles/count')
  @response(200, {
    description: 'OtherRole model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(OtherRole) where?: Where<OtherRole>,
  ): Promise<Count> {
    return this.otherRoleRepository.count(where);
  }

  @get('/other-roles')
  @response(200, {
    description: 'Array of OtherRole model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(OtherRole, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(OtherRole) filter?: Filter<OtherRole>,
  ): Promise<OtherRole[]> {
    return this.otherRoleRepository.find(filter);
  }

  @patch('/other-roles')
  @response(200, {
    description: 'OtherRole PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(OtherRole, {partial: true}),
        },
      },
    })
    otherRole: OtherRole,
    @param.where(OtherRole) where?: Where<OtherRole>,
  ): Promise<Count> {
    return this.otherRoleRepository.updateAll(otherRole, where);
  }

  @get('/other-roles/{id}')
  @response(200, {
    description: 'OtherRole model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(OtherRole, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(OtherRole, {exclude: 'where'}) filter?: FilterExcludingWhere<OtherRole>
  ): Promise<OtherRole> {
    return this.otherRoleRepository.findById(id, filter);
  }

  @patch('/other-roles/{id}')
  @response(204, {
    description: 'OtherRole PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(OtherRole, {partial: true}),
        },
      },
    })
    otherRole: OtherRole,
  ): Promise<void> {
    await this.otherRoleRepository.updateById(id, otherRole);
  }

  @put('/other-roles/{id}')
  @response(204, {
    description: 'OtherRole PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() otherRole: OtherRole,
  ): Promise<void> {
    await this.otherRoleRepository.replaceById(id, otherRole);
  }

  @del('/other-roles/{id}')
  @response(204, {
    description: 'OtherRole DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.otherRoleRepository.deleteById(id);
  }
}
