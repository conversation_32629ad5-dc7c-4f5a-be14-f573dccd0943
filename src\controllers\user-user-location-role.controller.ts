import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  User,
  UserLocationRole,
} from '../models';
import {UserRepository} from '../repositories';

export class UserUserLocationRoleController {
  constructor(
    @repository(UserRepository) protected userRepository: UserRepository,
  ) { }

  @get('/users/{id}/user-location-roles', {
    responses: {
      '200': {
        description: 'Array of User has many UserLocationRole',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(UserLocationRole)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<UserLocationRole>,
  ): Promise<UserLocationRole[]> {
    return this.userRepository.userLocationRoles(id).find(filter);
  }

  @post('/users/{id}/user-location-roles', {
    responses: {
      '200': {
        description: 'User model instance',
        content: {'application/json': {schema: getModelSchemaRef(UserLocationRole)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof User.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(UserLocationRole, {
            title: 'NewUserLocationRoleInUser',
            exclude: ['id'],
            optional: ['userId']
          }),
        },
      },
    }) userLocationRole: Omit<UserLocationRole, 'id'>,
  ): Promise<UserLocationRole> {
    return this.userRepository.userLocationRoles(id).create(userLocationRole);
  }

  @patch('/users/{id}/user-location-roles', {
    responses: {
      '200': {
        description: 'User.UserLocationRole PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(UserLocationRole, {partial: true}),
        },
      },
    })
    userLocationRole: Partial<UserLocationRole>,
    @param.query.object('where', getWhereSchemaFor(UserLocationRole)) where?: Where<UserLocationRole>,
  ): Promise<Count> {
    return this.userRepository.userLocationRoles(id).patch(userLocationRole, where);
  }

  @del('/users/{id}/user-location-roles', {
    responses: {
      '200': {
        description: 'User.UserLocationRole DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(UserLocationRole)) where?: Where<UserLocationRole>,
  ): Promise<Count> {
    return this.userRepository.userLocationRoles(id).delete(where);
  }
}
