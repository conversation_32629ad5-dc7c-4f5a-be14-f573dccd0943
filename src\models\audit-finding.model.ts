import {Entity, model, property, belongsTo} from '@loopback/repository';
import {AuditGmsThree} from './audit-gms-three.model';
import {Audit} from './audit.model';
import {User} from './user.model';

@model()
export class AuditFinding extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  classification?: string;

  @property({
    type: 'string',
  })
  findings?: string;

  @property({
    type: 'string',
  })
  remarks?: string;

  @property({
    type: 'object',
  })
  inspectionCategories?: object;

  @property({
    type: 'string',
  })
  potentialHazard?: string;

  @property({
    type: 'string',
  })
  recommendations?: string;

  @property({
    type: 'array',
    itemType: 'string',
  })
  uploads?: string[];

  @property({
    type: 'string',
  })
  standardsAndReferences?: string;

  @property({
    type: 'string',
  })
  timeFrame?: string;

  @property({
    type: 'string',
  })
  category?: string;

  @property({
    type: 'string',
  })
  maskId?: string;

  @property({
    type: 'string',
  })
  created?: string;
  
  @property({
    type: 'string',
  })
  dueDate?: string;

  @property({
    type: 'boolean',
  })
  actionAssigned?: boolean;
  
  @belongsTo(() => AuditGmsThree)
  auditGmsThreeId: string;

  @belongsTo(() => Audit)
  auditId: string;

  @belongsTo(() => User)
  assignedToId: string;

  constructor(data?: Partial<AuditFinding>) {
    super(data);
  }
}

export interface AuditFindingRelations {
  // describe navigational properties here
}

export type AuditFindingWithRelations = AuditFinding & AuditFindingRelations;
