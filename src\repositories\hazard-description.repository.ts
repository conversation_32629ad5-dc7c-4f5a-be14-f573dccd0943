import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {HazardDescription, HazardDescriptionRelations, HazardGms} from '../models';
import {HazardGmsRepository} from './hazard-gms.repository';

export class HazardDescriptionRepository extends DefaultCrudRepository<
  HazardDescription,
  typeof HazardDescription.prototype.id,
  HazardDescriptionRelations
> {

  public readonly hazardGms: HasManyRepositoryFactory<HazardGms, typeof HazardDescription.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('HazardGmsRepository') protected hazardGmsRepositoryGetter: Getter<HazardGmsRepository>,
  ) {
    super(HazardDescription, dataSource);
    this.hazardGms = this.createHasManyRepositoryFactoryFor('hazardGms', hazardGmsRepositoryGetter,);
    this.registerInclusionResolver('hazardGms', this.hazardGms.inclusionResolver);
  }
}
