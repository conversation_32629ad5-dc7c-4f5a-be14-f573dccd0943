import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {GhsThree, GhsThreeRelations, GhsTwo} from '../models';
import {GhsTwoRepository} from './ghs-two.repository';

export class GhsThreeRepository extends DefaultCrudRepository<
  GhsThree,
  typeof GhsThree.prototype.id,
  GhsThreeRelations
> {

  public readonly ghsTwo: BelongsToAccessor<GhsTwo, typeof GhsThree.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('GhsTwoRepository') protected ghsTwoRepositoryGetter: Getter<GhsTwoRepository>,
  ) {
    super(GhsThree, dataSource);
    this.ghsTwo = this.createBelongsToAccessorFor('ghsTwo', ghsTwoRepositoryGetter,);
    this.registerInclusionResolver('ghsTwo', this.ghsTwo.inclusionResolver);
  }
}
