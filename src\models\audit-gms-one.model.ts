import { Entity, model, property, hasMany} from '@loopback/repository';
import {AuditGmsTwo} from './audit-gms-two.model';

@model()
export class AuditGmsOne extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;
  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'number',
  })
  order?: number;

  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'string',
  })
  modified?: string;

  @hasMany(() => AuditGmsTwo)
  auditGmsTwos: AuditGmsTwo[];

  constructor(data?: Partial<AuditGmsOne>) {
    super(data);
  }
}

export interface AuditGmsOneRelations {
  // describe navigational properties here
}

export type AuditGmsOneWithRelations = AuditGmsOne & AuditGmsOneRelations;
