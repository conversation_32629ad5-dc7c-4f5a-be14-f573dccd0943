import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import { ReportData } from '../models';
import { ReportDataRepository, ActionRepository } from '../repositories';

export class ReportDataController {
  constructor(
    @repository(ReportDataRepository)
    public reportDataRepository: ReportDataRepository,
    @repository(ActionRepository)
    public actionRepository: ActionRepository,
  ) { }

  @post('/report-data')
  @response(200, {
    description: 'ReportData model instance',
    content: { 'application/json': { schema: getModelSchemaRef(ReportData) } },
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ReportData, {
            title: 'NewReportData',
            exclude: ['id'],
          }),
        },
      },
    })
    reportData: Omit<ReportData, 'id'>,
  ): Promise<ReportData> {
    return this.reportDataRepository.create(reportData);
  }

  @get('/report-data/count')
  @response(200, {
    description: 'ReportData model count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async count(
    @param.where(ReportData) where?: Where<ReportData>,
  ): Promise<Count> {
    return this.reportDataRepository.count(where);
  }

  @get('/report-data')
  @response(200, {
    description: 'Array of ReportData model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ReportData, { includeRelations: true }),
        },
      },
    },
  })
  async find(
    @param.filter(ReportData) filter?: Filter<ReportData>,
  ): Promise<ReportData[]> {
    return this.reportDataRepository.find(filter);
  }

  @patch('/report-data')
  @response(200, {
    description: 'ReportData PATCH success count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ReportData, { partial: true }),
        },
      },
    })
    reportData: ReportData,
    @param.where(ReportData) where?: Where<ReportData>,
  ): Promise<Count> {
    return this.reportDataRepository.updateAll(reportData, where);
  }

  @get('/report-data/{id}')
  @response(200, {
    description: 'ReportData model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(ReportData, { includeRelations: true }),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(ReportData, { exclude: 'where' }) filter?: FilterExcludingWhere<ReportData>
  ): Promise<ReportData> {
    return this.reportDataRepository.findById(id, filter);
  }

  @patch('/report-data/{id}')
  @response(204, {
    description: 'ReportData PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ReportData, { partial: true }),
        },
      },
    })
    reportData: ReportData,
  ): Promise<void> {
    await this.reportDataRepository.updateById(id, reportData);
  }

  @put('/report-data/{id}')
  @response(204, {
    description: 'ReportData PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() reportData: ReportData,
  ): Promise<void> {
    await this.reportDataRepository.replaceById(id, reportData);
  }

  @del('/report-data/{id}')
  @response(204, {
    description: 'ReportData DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.reportDataRepository.deleteById(id);
    await this.actionRepository.deleteAll({ objectId: id })
  }
}
