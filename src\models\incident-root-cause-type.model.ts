import {Entity, model, property, hasMany} from '@loopback/repository';
import {IncidentRootCauseDescription} from './incident-root-cause-description.model';

@model()
export class IncidentRootCauseType extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  description?: string;

  @hasMany(() => IncidentRootCauseDescription)
  incidentRootCauseDescriptions: IncidentRootCauseDescription[];

  constructor(data?: Partial<IncidentRootCauseType>) {
    super(data);
  }
}

export interface IncidentRootCauseTypeRelations {
  // describe navigational properties here
}

export type IncidentRootCauseTypeWithRelations = IncidentRootCauseType & IncidentRootCauseTypeRelations;
