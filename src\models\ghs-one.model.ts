import {Entity, model, property, hasMany} from '@loopback/repository';
import {GhsTwo} from './ghs-two.model';

@model({settings: {strict: false}})
export class GhsOne extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'number',
  })
  order?: number;
  
  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'string',
  })
  modified?: string;

  @hasMany(() => GhsTwo)
  ghsTwos: GhsTwo[];
  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<GhsOne>) {
    super(data);
  }
}

export interface GhsOneRelations {
  // describe navigational properties here
}

export type GhsOneWithRelations = GhsOne & GhsOneRelations;
