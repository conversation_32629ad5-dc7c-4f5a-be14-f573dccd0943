import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {PlantRole, PlantRoleRelations} from '../models';

export class PlantRoleRepository extends DefaultCrudRepository<
  PlantRole,
  typeof PlantRole.prototype.id,
  PlantRoleRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(PlantRole, dataSource);
  }
}
