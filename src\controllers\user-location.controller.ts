import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import { UserLocation } from '../models';
import { UserLocationRepository } from '../repositories';
import { authenticate } from '@loopback/authentication';
@authenticate('jwt')
export class UserLocationController {
  constructor(
    @repository(UserLocationRepository)
    public userLocationRepository: UserLocationRepository,
  ) { }

  @post('/user-locations')
  @response(200, {
    description: 'UserLocation model instance',
    content: { 'application/json': { schema: getModelSchemaRef(UserLocation) } },
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              userIds: {
                type: 'array',
                items: {
                  type: 'string',
                },
              },
              deselectUserIds: {
                type: 'array',
                items: {
                  type: 'string',
                },
              },
              locations: {
                type: 'object',
                properties: {
                  locationOne: {
                    type: 'array',
                    items: {
                      type: 'string',
                    },
                  },
                  locationTwo: {
                    type: 'array',
                    items: {
                      type: 'string',
                    },
                  },
                  locationThree: {
                    type: 'array',
                    items: {
                      type: 'string',
                    },
                  },
                  locationFour: {
                    type: 'array',
                    items: {
                      type: 'string',
                    },
                  },
                  locationFive: {
                    type: 'array',
                    items: {
                      type: 'string',
                    },
                  },
                  locationSix: {
                    type: 'array',
                    items: {
                      type: 'string',
                    },
                  },
                  // Repeat the pattern for locationThree, locationFour, locationFive, and locationSix
                },
              },
            },
          },
        },
      },
    })
    requestBody: { userIds: string[], deselectUserIds: string[], locations: { [key: string]: object[] } },
  ): Promise<UserLocation[]> {
    const userLocations: UserLocation[] = [];

    for (const userId of requestBody.userIds) {
      let userLocation: any = await this.userLocationRepository.findOne({ where: { userId } }) || false;

      if (userLocation) {
        for (const locationKey in requestBody.locations) {
          if (requestBody.locations.hasOwnProperty(locationKey)) {
            const newLocationValues = requestBody.locations[locationKey];
            const existingLocationValues = userLocation.locations[locationKey];

            // Filter out the values that are already present in the existing array
            const uniqueLocationValues = newLocationValues.filter(value => !existingLocationValues.includes(value));

            userLocation.locations[locationKey] = [...existingLocationValues, ...uniqueLocationValues];
          }
        }

        userLocation = await this.userLocationRepository.updateById(userLocation.id, userLocation);
      } else {
        userLocation = new UserLocation();
        userLocation.userId = userId;
        userLocation.locations = requestBody.locations;
        // Set other properties like created, modified if needed
        userLocation = await this.userLocationRepository.create(userLocation);
      }

      userLocations.push(userLocation);
    }

    for (const userId of requestBody.deselectUserIds) {
      let userLocation: any = await this.userLocationRepository.findOne({ where: { userId } }) || false;

      if (userLocation) {
        for (const locationKey in requestBody.locations) {
          if (requestBody.locations.hasOwnProperty(locationKey)) {
            const newLocationValues = requestBody.locations[locationKey];
            const existingLocationValues = userLocation.locations[locationKey];

            // Filter out the values that are already present in the existing array
            const updatedLocationValues = existingLocationValues.filter((value: any) => !newLocationValues.includes(value));

            userLocation.locations[locationKey] = updatedLocationValues;
          }
        }

        userLocation = await this.userLocationRepository.updateById(userLocation.id, userLocation);
      }
    }

    return userLocations;
  }

  @get('/user-locations/count')
  @response(200, {
    description: 'UserLocation model count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async count(
    @param.where(UserLocation) where?: Where<UserLocation>,
  ): Promise<Count> {
    return this.userLocationRepository.count(where);
  }

  @get('/user-locations')
  @response(200, {
    description: 'Array of UserLocation model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(UserLocation, { includeRelations: true }),
        },
      },
    },
  })
  async find(
    @param.filter(UserLocation) filter?: Filter<UserLocation>,
  ): Promise<UserLocation[]> {
    return this.userLocationRepository.find(filter);
  }

  @patch('/user-locations')
  @response(200, {
    description: 'UserLocation PATCH success count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(UserLocation, { partial: true }),
        },
      },
    })
    userLocation: UserLocation,
    @param.where(UserLocation) where?: Where<UserLocation>,
  ): Promise<Count> {
    return this.userLocationRepository.updateAll(userLocation, where);
  }

  @get('/user-locations/{id}')
  @response(200, {
    description: 'UserLocation model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(UserLocation, { includeRelations: true }),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(UserLocation, { exclude: 'where' }) filter?: FilterExcludingWhere<UserLocation>
  ): Promise<UserLocation> {
    return this.userLocationRepository.findById(id, filter);
  }

  @patch('/user-locations/{id}')
  @response(204, {
    description: 'UserLocation PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(UserLocation, { partial: true }),
        },
      },
    })
    userLocation: UserLocation,
  ): Promise<void> {
    await this.userLocationRepository.updateById(id, userLocation);
  }

  @put('/user-locations/{id}')
  @response(204, {
    description: 'UserLocation PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() userLocation: UserLocation,
  ): Promise<void> {
    await this.userLocationRepository.replaceById(id, userLocation);
  }

  @del('/user-locations/{id}')
  @response(204, {
    description: 'UserLocation DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.userLocationRepository.deleteById(id);
  }
}
