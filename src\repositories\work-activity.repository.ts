import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {WorkActivity, WorkActivityRelations} from '../models';

export class WorkActivityRepository extends DefaultCrudRepository<
  WorkActivity,
  typeof WorkActivity.prototype.id,
  WorkActivityRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(WorkActivity, dataSource);
  }
}
