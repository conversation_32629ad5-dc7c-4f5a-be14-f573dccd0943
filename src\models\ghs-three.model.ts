import { Entity, model, property, belongsTo} from '@loopback/repository';
import {GhsTwo} from './ghs-two.model';

@model()
export class GhsThree extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'string',
  })
  modified?: string;

  @property({
    type: 'number',
  })
  order?: number;

  @belongsTo(() => GhsTwo)
  ghsTwoId: string;

  constructor(data?: Partial<GhsThree>) {
    super(data);
  }
}

export interface GhsThreeRelations {
  // describe navigational properties here
}

export type GhsThreeWithRelations = GhsThree & GhsThreeRelations;
