import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {GhsThree} from '../models';
import {GhsThreeRepository} from '../repositories';

export class GhsThreeController {
  constructor(
    @repository(GhsThreeRepository)
    public ghsThreeRepository : GhsThreeRepository,
  ) {}

  @post('/ghs-threes')
  @response(200, {
    description: 'GhsThree model instance',
    content: {'application/json': {schema: getModelSchemaRef(GhsThree)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GhsThree, {
            title: 'NewGhsThree',
            exclude: ['id'],
          }),
        },
      },
    })
    ghsThree: Omit<GhsThree, 'id'>,
  ): Promise<GhsThree> {
    return this.ghsThreeRepository.create(ghsThree);
  }

  @get('/ghs-threes/count')
  @response(200, {
    description: 'GhsThree model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(GhsThree) where?: Where<GhsThree>,
  ): Promise<Count> {
    return this.ghsThreeRepository.count(where);
  }

  @get('/ghs-threes')
  @response(200, {
    description: 'Array of GhsThree model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(GhsThree, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(GhsThree) filter?: Filter<GhsThree>,
  ): Promise<GhsThree[]> {
    return this.ghsThreeRepository.find(filter);
  }

  @patch('/ghs-threes')
  @response(200, {
    description: 'GhsThree PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GhsThree, {partial: true}),
        },
      },
    })
    ghsThree: GhsThree,
    @param.where(GhsThree) where?: Where<GhsThree>,
  ): Promise<Count> {
    return this.ghsThreeRepository.updateAll(ghsThree, where);
  }

  @get('/ghs-threes/{id}')
  @response(200, {
    description: 'GhsThree model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(GhsThree, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(GhsThree, {exclude: 'where'}) filter?: FilterExcludingWhere<GhsThree>
  ): Promise<GhsThree> {
    return this.ghsThreeRepository.findById(id, filter);
  }

  @patch('/ghs-threes/{id}')
  @response(204, {
    description: 'GhsThree PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GhsThree, {partial: true}),
        },
      },
    })
    ghsThree: GhsThree,
  ): Promise<void> {
    await this.ghsThreeRepository.updateById(id, ghsThree);
  }

  @put('/ghs-threes/{id}')
  @response(204, {
    description: 'GhsThree PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() ghsThree: GhsThree,
  ): Promise<void> {
    await this.ghsThreeRepository.replaceById(id, ghsThree);
  }

  @del('/ghs-threes/{id}')
  @response(204, {
    description: 'GhsThree DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.ghsThreeRepository.deleteById(id);
  }
}
