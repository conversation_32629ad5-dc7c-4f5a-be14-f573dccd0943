import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {LocationFive, LocationFiveRelations, LocationSix} from '../models';
import {LocationSixRepository} from './location-six.repository';

export class LocationFiveRepository extends DefaultCrudRepository<
  LocationFive,
  typeof LocationFive.prototype.id,
  LocationFiveRelations
> {

  public readonly locationSixes: HasManyRepositoryFactory<LocationSix, typeof LocationFive.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('LocationSixRepository') protected locationSixRepositoryGetter: Getter<LocationSixRepository>,
  ) {
    super(LocationFive, dataSource);
    this.locationSixes = this.createHasManyRepositoryFactoryFor('locationSixes', locationSixRepositoryGetter,);
    this.registerInclusionResolver('locationSixes', this.locationSixes.inclusionResolver);
  }
}
