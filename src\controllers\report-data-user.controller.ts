import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ReportData,
  User,
} from '../models';
import {ReportDataRepository} from '../repositories';

export class ReportDataUserController {
  constructor(
    @repository(ReportDataRepository)
    public reportDataRepository: ReportDataRepository,
  ) { }

  @get('/report-data/{id}/user', {
    responses: {
      '200': {
        description: 'User belonging to ReportData',
        content: {
          'application/json': {
            schema: getModelSchemaRef(User),
          },
        },
      },
    },
  })
  async getUser(
    @param.path.string('id') id: typeof ReportData.prototype.id,
  ): Promise<User> {
    return this.reportDataRepository.user(id);
  }
}
