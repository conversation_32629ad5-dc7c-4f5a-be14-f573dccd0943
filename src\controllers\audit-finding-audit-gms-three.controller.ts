import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  AuditFinding,
  AuditGmsThree,
} from '../models';
import {AuditFindingRepository} from '../repositories';

export class AuditFindingAuditGmsThreeController {
  constructor(
    @repository(AuditFindingRepository)
    public auditFindingRepository: AuditFindingRepository,
  ) { }

  @get('/audit-findings/{id}/audit-gms-three', {
    responses: {
      '200': {
        description: 'AuditGmsThree belonging to AuditFinding',
        content: {
          'application/json': {
            schema: getModelSchemaRef(AuditGmsThree),
          },
        },
      },
    },
  })
  async getAuditGmsThree(
    @param.path.string('id') id: typeof AuditFinding.prototype.id,
  ): Promise<AuditGmsThree> {
    return this.auditFindingRepository.auditGmsThree(id);
  }
}
