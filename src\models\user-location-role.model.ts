import {Entity, model, property} from '@loopback/repository';

@model()
export class UserLocationRole extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'array',
    itemType: 'string',
  })
  ehsRoles?: string[];

  @property({
    type: 'array',
    itemType: 'string',
  })
  eptwRoles?: string[];

  @property({
    type: 'array',
    itemType: 'string',
  })
  incidentRoles?: string[];

  @property({
    type: 'array',
    itemType: 'string',
  })
  inspectionRoles?: string[];

  @property({
    type: 'array',
    itemType: 'string',
  })
  plantRoles?: string[];

  @property({
    type: 'array',
    itemType: 'string',
  })
  roles?: string[];

  @property({
    type: 'string',
  })
  userId?: string;

  @property({
    type: 'string',
  })
  locationOneId?: string;

  @property({
    type: 'string',
  })
  locationTwoId?: string;

  @property({
    type: 'string',
  })
  locationThreeId?: string;

  @property({
    type: 'string',
  })
  locationFourId?: string;

  constructor(data?: Partial<UserLocationRole>) {
    super(data);
  }
}

export interface UserLocationRoleRelations {
  // describe navigational properties here
}

export type UserLocationRoleWithRelations = UserLocationRole & UserLocationRoleRelations;
