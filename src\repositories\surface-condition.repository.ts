import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {SurfaceCondition, SurfaceConditionRelations} from '../models';

export class SurfaceConditionRepository extends DefaultCrudRepository<
  SurfaceCondition,
  typeof SurfaceCondition.prototype.id,
  SurfaceConditionRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(SurfaceCondition, dataSource);
  }
}
