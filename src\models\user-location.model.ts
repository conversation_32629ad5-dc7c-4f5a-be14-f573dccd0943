import { Entity, model, property, belongsTo} from '@loopback/repository';
import {User} from './user.model';

@model()
export class UserLocation extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'object',
    properties: {
      locationOne: {
        type: 'array',
        itemType: 'string',
      },
      locationTwo: {
        type: 'array',
        itemType: 'string',
      },
      locationThree: {
        type: 'array',
        itemType: 'string',
      },
      locationFour: {
        type: 'array',
        itemType: 'string',
      },
      locationFive: {
        type: 'array',
        itemType: 'string',
      },
      locationSix: {
        type: 'array',
        itemType: 'string',
      },
    },
  })
  locations?: {
    locationOne?: string[];
    locationTwo?: string[];
    locationThree?: string[];
    locationFour?: string[];
    locationFive?: string[];
    locationSix?: string[];
  };

  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'string',
  })
  modified?: string;

  @belongsTo(() => User)
  userId: string;

  constructor(data?: Partial<UserLocation>) {
    super(data);
  }
}

export interface UserLocationRelations {
  // describe navigational properties here
}

export type UserLocationWithRelations = UserLocation & UserLocationRelations;
