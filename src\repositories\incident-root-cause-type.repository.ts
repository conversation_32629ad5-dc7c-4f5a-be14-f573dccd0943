import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {IncidentRootCauseType, IncidentRootCauseTypeRelations, IncidentRootCauseDescription} from '../models';
import {IncidentRootCauseDescriptionRepository} from './incident-root-cause-description.repository';

export class IncidentRootCauseTypeRepository extends DefaultCrudRepository<
  IncidentRootCauseType,
  typeof IncidentRootCauseType.prototype.id,
  IncidentRootCauseTypeRelations
> {

  public readonly incidentRootCauseDescriptions: HasManyRepositoryFactory<IncidentRootCauseDescription, typeof IncidentRootCauseType.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('IncidentRootCauseDescriptionRepository') protected incidentRootCauseDescriptionRepositoryGetter: Getter<IncidentRootCauseDescriptionRepository>,
  ) {
    super(IncidentRootCauseType, dataSource);
    this.incidentRootCauseDescriptions = this.createHasManyRepositoryFactoryFor('incidentRootCauseDescriptions', incidentRootCauseDescriptionRepositoryGetter,);
    this.registerInclusionResolver('incidentRootCauseDescriptions', this.incidentRootCauseDescriptions.inclusionResolver);
  }
}
