// Copyright IBM Corp. 2020. All Rights Reserved.
// Node module: loopback4-example-shopping
// This file is licensed under the MIT License.
// License text available at https://opensource.org/licenses/MIT

import {
  authenticate,
  TokenService,
  UserService
} from '@loopback/authentication';
import { TokenServiceBindings } from '@loopback/authentication-jwt';
import { authorize } from '@loopback/authorization';
import { inject } from '@loopback/core';
import { model, property, repository } from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  HttpErrors,
  param,
  patch,
  post,
  put,
  requestBody,
  ResponseObject,
  RestBindings,
  Request,
  response
} from '@loopback/rest';
import { SecurityBindings, securityId, UserProfile } from '@loopback/security';
import isemail from 'isemail';
import _ from 'lodash';
import { SentMessageInfo } from 'nodemailer';
import { PasswordHasherBindings, UserServiceBindings } from '../keys';
import { KeyAndPassword, ResetPasswordInit, User } from '../models';
import { Credentials, UserRepository, UserCredentialsRepository, UserLocationRoleRepository } from '../repositories';
import {
  basicAuthorization,
  PasswordHasher,

  UserManagementService,
  validateCredentials,
  validateKeyPassword
} from '../services';
import { OPERATION_SECURITY_SPEC } from '../utils';
import {
  CredentialsRequestBody,
  PasswordResetRequestBody,
  UserProfileSchema
} from './specs/user-controller.specs';
import axios from 'axios';
import { CognitoIdentityServiceProvider } from 'aws-sdk';
import { generateRandomPassword } from '../utils/passwordUtils';
import { SqsService } from '../services/sqs-service.service';
import { google } from 'googleapis';


const cognito = new CognitoIdentityServiceProvider({
  region: process.env.AWS_REGION,
  accessKeyId: process.env.AWS_COGNITO_ACCESS_KEY,
  secretAccessKey: process.env.AWS_COGNITO_SECRET_KEY
})


@model()
export class NewUserRequest extends User {
  @property({
    type: 'string',
    required: true,
  })
  password: string;
}

export class PasswordResetRequest {
  @property({
    type: 'string',
    required: true,
  })
  email: string;
}

export class UserManagementController {
  constructor(
    @repository(UserRepository)
    public userRepository: UserRepository,
    @repository(UserCredentialsRepository)
    public userCredentialRepository: UserCredentialsRepository,
    @repository(UserLocationRoleRepository)
    public userLocationRoleRepository: UserLocationRoleRepository,
    @inject(PasswordHasherBindings.PASSWORD_HASHER)
    public passwordHasher: PasswordHasher,
    @inject(TokenServiceBindings.TOKEN_SERVICE)
    public jwtService: TokenService,
    @inject(UserServiceBindings.USER_SERVICE)
    public userService: UserService<User, Credentials>,
    @inject(UserServiceBindings.USER_SERVICE)
    public userManagementService: UserManagementService,
    @inject('services.SqsService')
    public sqsService: SqsService,

  ) { }

  @post('/users', {
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })

  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewUserRequest, {
            title: 'NewUser',
          }),
        },
      },
    })
    newUserRequest: NewUserRequest,
  ): Promise<User> {
    // All new users have the "customer" role by default
    newUserRequest.roles = ['user'];
    newUserRequest.type = 'Internal';
    newUserRequest.status = true;
    // ensure a valid email value and password value
    // validateCredentials(_.pick(newUserRequest, ['email', 'password']));

    try {
      newUserRequest.resetKey = '';
      return await this.userManagementService.createUser(newUserRequest);
    } catch (error) {
      // MongoError 11000 duplicate key
      if (error.code === 11000 && error.errmsg.includes('index: uniqueEmail')) {
        throw new HttpErrors.Conflict('Email value is already taken');
      } else {
        throw error;
      }
    }
  }

  @post('/users/external', {
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })

  async createExternal(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(User, {
            title: 'NewUser',
          }),
        },
      },
    })
    newUserRequest: NewUserRequest,
  ): Promise<User> {
    // All new users have the "customer" role by default
    newUserRequest.roles = ['user'];
    newUserRequest.type = 'External';
    newUserRequest.status = true;
    // ensure a valid email value and password value
    // validateCredentials(_.pick(newUserRequest, ['email', 'password']));

    try {
      newUserRequest.resetKey = '';
      const tempPassword = generateRandomPassword();
      newUserRequest.password = tempPassword;

      const params = {
        UserPoolId: `${process.env.AWS_EXTERNAL_USER_POOL_ID}`,
        Username: newUserRequest.email,
        TemporaryPassword: tempPassword,
        MessageAction: 'SUPPRESS',
        UserAttributes: [
          {
            Name: 'email',
            Value: newUserRequest.email
          },
          {
            Name: 'email_verified',
            Value: 'true'
          }
        ]
      };

      const awsNewUser = await cognito.adminCreateUser(params).promise();
      console.log(awsNewUser)
      if (awsNewUser.User?.Username) {

        newUserRequest.id = awsNewUser.User.Username;
        const newUser = await this.userManagementService.createUser(newUserRequest);
        await this.sqsService.sendMessage(newUser, 'Account has been created', `Please click the link <a href="https://stt-gdc.acuizen.com"> https://stt-gdc.acuizen.com </a>and login with your email and given password ${tempPassword}`);
        return newUser;
      } else {
        throw new HttpErrors.NotFound(`User not created. Try again`);
      }
    } catch (error) {
      // MongoError 11000 duplicate key
      if (error.code === 11000 && error.errmsg.includes('index: uniqueEmail')) {
        throw new HttpErrors.Conflict('Email value is already taken');
      } else {
        throw error;
      }
    }
  }

  @post('/users/external/reset-password', {
    responses: {
      '200': {
        description: 'Password Reset Success',
        content: { 'application/json': { schema: { type: 'object' } } },
      },
    },
  })

  async resetPasswordExternal(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PasswordResetRequest, {
            title: 'PasswordReset',
          }),
        },
      },
    })
    passwordResetRequest: PasswordResetRequest,
  ): Promise<any> {
    // All new users have the "customer" role by default

    // ensure a valid email value and password value
    // validateCredentials(_.pick(newUserRequest, ['email', 'password']));

    try {

      const tempPassword = generateRandomPassword();
      const email = passwordResetRequest.email

      const params = {
        UserPoolId: `${process.env.AWS_EXTERNAL_USER_POOL_ID}`,
        Username: email,
        Password: tempPassword,
        Permanent: false
      };

      // await cognito.adminSetUserMFAPreference({
      //   UserPoolId: `${process.env.AWS_EXTERNAL_USER_POOL_ID}`,
      //   Username: email,
      //   SoftwareTokenMfaSettings: { Enabled: false, PreferredMfa: false },
      // }).promise();

      const awsNewUser = await cognito.adminSetUserPassword(params).promise();

      this.sqsService.sendEmail(email, 'Account Password has been created', `Please click the link <a href="https://stt-gdc.acuizen.com"> https://stt-gdc.acuizen.com </a>and login with your email and given password ${tempPassword}`);
      return { message: 'Password updated successfully' };
    } catch (error) {

    }

  }


  @post('/users/external/reset-mfa', {
    responses: {
      '200': {
        description: 'MFA Reset Success',
        content: { 'application/json': { schema: { type: 'object' } } },
      },
    },
  })

  async resetMFAExternal(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PasswordResetRequest, {
            title: 'PasswordReset',
          }),
        },
      },
    })
    passwordResetRequest: PasswordResetRequest,
  ): Promise<any> {
    // All new users have the "customer" role by default
    const tempPassword = generateRandomPassword();
    const email = passwordResetRequest.email
    // ensure a valid email value and password value
    // validateCredentials(_.pick(newUserRequest, ['email', 'password']));
    try {
      await cognito.adminDeleteUser({
        UserPoolId: `${process.env.AWS_EXTERNAL_USER_POOL_ID}`,
        Username: email // The username or email of the user to delete
      }).promise();
    } catch (error) {
      console.error("Failed to delete user:", error);
      throw new Error("Failed to delete user.");
    }
    try {
      // Create the user with the same username (email) and temporary password
      const params = {
        UserPoolId: `${process.env.AWS_EXTERNAL_USER_POOL_ID}`,
        Username: email,
        TemporaryPassword: tempPassword,
        MessageAction: 'SUPPRESS',
        UserAttributes: [
          {
            Name: 'email',
            Value: email
          },
          {
            Name: 'email_verified',
            Value: 'true'
          }
        ]
      };

      const awsNewUser = await cognito.adminCreateUser(params).promise();

      // Optionally, set the user's password (if you don't want it to be the temporary one) and enable MFA here

    } catch (error) {
      console.error("Failed to create user:", error);
      throw new Error("Failed to create user.");
    }

    this.sqsService.sendEmail(email, 'MFA has been reset', `Please click the link <a href="https://stt-gdc.acuizen.com"> https://stt-gdc.acuizen.com </a>and login with your email and given password ${tempPassword}`);
    return { message: 'MFA Reset updated successfully' };


  }


  @post('/users/external/delete', {
    responses: {
      '200': {
        description: 'Delete User Success',
        content: { 'application/json': { schema: { type: 'object' } } },
      },
    },
  })

  async deleteExternal(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PasswordResetRequest, {
            title: 'PasswordReset',
          }),
        },
      },
    })
    passwordResetRequest: PasswordResetRequest,
  ): Promise<any> {
    // All new users have the "customer" role by default

    const email = passwordResetRequest.email
    // ensure a valid email value and password value
    // validateCredentials(_.pick(newUserRequest, ['email', 'password']));
    const user = await this.userRepository.findOne({ where: { email: email, type: 'External' } });
    if (!user) throw new Error('User not found')
    await this.userRepository.updateById(user.id, { status: false })

    if (!email) return true;
    try {
      await cognito.adminDeleteUser({
        UserPoolId: `${process.env.AWS_EXTERNAL_USER_POOL_ID}`,
        Username: email // The username or email of the user to delete
      }).promise();



    } catch (error) {
      console.error("Failed to delete user:", error);
      throw new Error("Failed to delete user.");
    }


    return { message: 'User Deleted' };


  }



  @del('/users/{id}')
  @response(204, {
    description: 'User DELETE success',
  })

  async deleteUser(@param.path.string('id') id: string): Promise<void> {
    const user = await this.userRepository.findById(id);

    if (!user) {
      throw new HttpErrors.NotFound(`User with id ${id} not found.`);
    }

    await this.userRepository.updateById(user.id, { status: false })

  }




  @authenticate('jwt')
  @patch('/users/{userId}', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })

  async set(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('userId') userId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(User, { partial: true }),
        },
      },
      description: 'update user',
    })
    user: Partial<User>,
  ): Promise<void> {
    try {
      // Only admin can assign roles

      // console.log(user)
      const originalUser = await this.userRepository.findById(userId)
      if (user.email) {
        const userEmail = user?.email || '';
        await cognito.adminUpdateUserAttributes({
          UserPoolId: `${process.env.AWS_EXTERNAL_USER_POOL_ID}`,
          Username: originalUser.email,
          UserAttributes: [
            {
              Name: 'email',
              Value: userEmail
            },
            {
              Name: 'email_verified', // Consider the implications of automatically setting this to true
              Value: 'true'
            }
          ]
        }).promise();
      }

      return await this.userRepository.updateById(userId, user);
    } catch (e) {
      console.log(e)
      return e;
    }
  }



  @patch('/bulkusers', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })


  async bulkSet(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              users: {
                type: 'array',
                items: getModelSchemaRef(User, { partial: true }),
              },
            },
          },
        },
      },
    })
    payload: { users: User[] },
  ): Promise<void> {
    try {
      const { users } = payload;

      await Promise.all(users.map(async (user) => {

        await this.userRepository.updateById(user.id, user);
      }));

    } catch (e) {
      // console.log(e)
      return e;
    }
  }

  @get('/users/{userId}', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })


  async findById(@param.path.string('userId') userId: string): Promise<User> {
    return this.userRepository.findById(userId);
  }

  @get('/internal/users/', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })


  async findByInternal(
    @param.query.string('search') search: string,
    @param.query.number('length') limit: number,
    @param.query.number('start') offset: number
  ): Promise<any> {
    const formData = new URLSearchParams();
    formData.append('grant_type', 'client_credentials');
    formData.append('client_id', `${process.env.AZURE_CLIENT_ID}`);
    formData.append('client_secret', `${process.env.AZURE_CLIENT_SECRET}`);
    formData.append('resource', 'https://graph.microsoft.com/');
    const accessResponse = await axios.post(`https://login.microsoftonline.com/${process.env.AZURE_TENANT_ID}/oauth2/token`, formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'

      }
    })

    console.log(search)
    let url = '';
    let customSearch = ''
    if (search) {
      customSearch = `"displayName:${search}"`;
      url = `https://graph.microsoft.com/v1.0/users?$top=50&$search=${customSearch}&$count=true`;
    } else {
      customSearch = '';
      url = `https://graph.microsoft.com/v1.0/users?$top=50&$count=true`;
    }
    try {
      const usersResponse = await axios.get(url, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessResponse.data.access_token}`,
          'consistencylevel': 'eventual'
        }
      })
      const users = await this.userRepository.find();
      const updatedUsers = usersResponse.data.value.map((user: { id: string; mail: string; }) => {
        const whiteListed = users.find(b => (b.email ?? '').trim().toLowerCase() === (user.mail ?? '').trim().toLowerCase());
        if (whiteListed) {

          return { ...user, country: whiteListed.country, status: 'active' };
        }
        return { ...user, status: 'inactive' };
      });
      return { data: updatedUsers, country: '', recordsTotal: usersResponse.data['@odata.count'] }
    }
    catch (e) {
      console.log(e)
    }



  }

  @get('/internal/users/beta', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })


  async findByInternalBeta(
    @param.query.string('search') search: string,
    @param.query.number('length') limit: number,
    @param.query.number('start') offset: number
  ): Promise<any> {
    const formData = new URLSearchParams();
    formData.append('grant_type', 'client_credentials');
    formData.append('client_id', `${process.env.AZURE_CLIENT_ID}`);
    formData.append('client_secret', `${process.env.AZURE_CLIENT_SECRET}`);
    formData.append('resource', 'https://graph.microsoft.com/');
    const accessResponse = await axios.post(`https://login.microsoftonline.com/${process.env.AZURE_TENANT_ID}/oauth2/token`, formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'

      }
    })



    try {

      const users = await this.userRepository.find();

      return { data: users, token: accessResponse.data.access_token }
    }
    catch (e) {
      console.log(e)
    }



  }


  @get('/thailand/users/', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })


  async findByThailand(
    @param.query.string('search') search: string,
    @param.query.number('length') limit: number,
    @param.query.number('start') offset: number
  ): Promise<any> {
    const formData = new URLSearchParams();
    formData.append('grant_type', 'client_credentials');
    formData.append('client_id', `${process.env.THAILAND_AZURE_CLIENT_ID}`);
    formData.append('client_secret', `${process.env.THAILAND_AZURE_CLIENT_SECRET}`);
    formData.append('resource', 'https://graph.microsoft.com/');
    const accessResponse = await axios.post(`https://login.microsoftonline.com/${process.env.THAILAND_AZURE_TENANT_ID}/oauth2/token`, formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'

      }
    })

    console.log(search)
    let url = '';
    let customSearch = ''
    if (search) {
      customSearch = `"displayName:${search}"`;
      url = `https://graph.microsoft.com/v1.0/users?$top=50&$search=${customSearch}&$count=true`;
    } else {
      customSearch = '';
      url = `https://graph.microsoft.com/v1.0/users?$top=50&$count=true`;
    }


    console.log(url)

    try {
      const usersResponse = await axios.get(url, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessResponse.data.access_token}`,
          'consistencylevel': 'eventual'
        }
      })
      const users = await this.userRepository.find();
      const updatedUsers = usersResponse.data.value.map((user: { id: string; mail: string; }) => {
        const whiteListed = users.find(b => (b.email ?? '').trim().toLowerCase() === (user.mail ?? '').trim().toLowerCase());
        if (whiteListed) {
          return { ...user, country: whiteListed.country, status: 'active' };
        }
        return { ...user, country: '', status: 'inactive' };
      });
      return { data: updatedUsers, recordsTotal: usersResponse.data['@odata.count'] }
    }
    catch (e) {
      console.log(e)
    }



  }

  @get('/uk/users/', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })


  async findByUk(
    @param.query.string('search') search: string,
    @param.query.number('length') limit: number,
    @param.query.number('start') offset: number
  ): Promise<any> {
    const formData = new URLSearchParams();
    formData.append('grant_type', 'client_credentials');
    formData.append('client_id', `${process.env.UK_AZURE_CLIENT_ID}`);
    formData.append('client_secret', `${process.env.UK_AZURE_CLIENT_SECRET}`);
    formData.append('resource', 'https://graph.microsoft.com/');
    const accessResponse = await axios.post(`https://login.microsoftonline.com/${process.env.UK_AZURE_TENANT_ID}/oauth2/token`, formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'

      }
    })

    console.log(search)
    let url = '';
    let customSearch = ''
    if (search) {
      customSearch = `"displayName:${search}"`;
      url = `https://graph.microsoft.com/v1.0/users?$top=50&$search=${customSearch}&$count=true`;
    } else {
      customSearch = '';
      url = `https://graph.microsoft.com/v1.0/users?$top=50&$count=true`;
    }


    console.log(url)

    try {
      const usersResponse = await axios.get(url, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessResponse.data.access_token}`,
          'consistencylevel': 'eventual'
        }
      })
      const users = await this.userRepository.find();
      const updatedUsers = usersResponse.data.value.map((user: { id: string; mail: string; }) => {
        const whiteListed = users.find(b => (b.email ?? '').trim().toLowerCase() === (user.mail ?? '').trim().toLowerCase());
        if (whiteListed) {
          return { ...user, country: whiteListed.country, status: 'active' };
        }
        return { ...user, country: '', status: 'inactive' };
      });
      return { data: updatedUsers, recordsTotal: usersResponse.data['@odata.count'] }
    }
    catch (e) {
      console.log(e)
    }



  }

  @get('/india/users/', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })


  async findByIndia(
    @param.query.string('search') search: string,
    @param.query.number('length') limit: number,
    @param.query.number('start') offset: number
  ): Promise<any> {
    const formData = new URLSearchParams();
    formData.append('grant_type', 'client_credentials');
    formData.append('client_id', `${process.env.INDIA_AZURE_CLIENT_ID}`);
    formData.append('client_secret', `${process.env.INDIA_AZURE_CLIENT_SECRET}`);
    formData.append('resource', 'https://graph.microsoft.com/');
    const accessResponse = await axios.post(`https://login.microsoftonline.com/${process.env.INDIA_AZURE_TENANT_ID}/oauth2/token`, formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'

      }
    })

    console.log(search)
    let url = '';
    let customSearch = ''
    if (search) {
      customSearch = `"displayName:${search}"`;
      url = `https://graph.microsoft.com/v1.0/users?$top=50&$search=${customSearch}&$count=true`;
    } else {
      customSearch = '';
      url = `https://graph.microsoft.com/v1.0/users?$top=50&$count=true`;
    }


    console.log(url)

    try {
      const usersResponse = await axios.get(url, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessResponse.data.access_token}`,
          'consistencylevel': 'eventual'
        }
      })
      const users = await this.userRepository.find();
      const updatedUsers = usersResponse.data.value.map((user: { id: string; mail: string; }) => {
        const whiteListed = users.find(b => (b.email ?? '').trim().toLowerCase() === (user.mail ?? '').trim().toLowerCase());
        if (whiteListed) {
          return { ...user, country: whiteListed.country, status: 'active' };
        }
        return { ...user, country: '', status: 'inactive' };
      });
      return { data: updatedUsers, recordsTotal: usersResponse.data['@odata.count'] }
    }
    catch (e) {
      console.log(e)
    }



  }

  @get('/korea/users/', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })


  async findByKorea(
    @param.query.string('search') search: string,
    @param.query.number('length') limit: number,
    @param.query.number('start') offset: number
  ): Promise<any> {
    const formData = new URLSearchParams();
    formData.append('grant_type', 'client_credentials');
    formData.append('client_id', `${process.env.KOREA_AZURE_CLIENT_ID}`);
    formData.append('client_secret', `${process.env.KOREA_AZURE_CLIENT_SECRET}`);
    formData.append('resource', 'https://graph.microsoft.com/');
    const accessResponse = await axios.post(`https://login.microsoftonline.com/${process.env.KOREA_AZURE_TENANT_ID}/oauth2/token`, formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'

      }
    })

    console.log(search)
    let url = '';
    let customSearch = ''
    if (search) {
      customSearch = `"displayName:${search}"`;
      url = `https://graph.microsoft.com/v1.0/users?$top=50&$search=${customSearch}&$count=true`;
    } else {
      customSearch = '';
      url = `https://graph.microsoft.com/v1.0/users?$top=50&$count=true`;
    }


    console.log(url)

    try {
      const usersResponse = await axios.get(url, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessResponse.data.access_token}`,
          'consistencylevel': 'eventual'
        }
      })
      const users = await this.userRepository.find();
      const updatedUsers = usersResponse.data.value.map((user: { id: string; mail: string; }) => {
        const whiteListed = users.find(b => (b.email ?? '').trim().toLowerCase() === (user.mail ?? '').trim().toLowerCase());
        if (whiteListed) {
          return { ...user, country: whiteListed.country, status: 'active' };
        }
        return { ...user, country: '', status: 'inactive' };
      });
      return { data: updatedUsers, recordsTotal: usersResponse.data['@odata.count'] }
    }
    catch (e) {
      console.log(e)
    }



  }


  @get('/phillipines/users/', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })


  async findByPhillipine(
    @param.query.string('search') search: string,
    @param.query.number('length') limit: number,
    @param.query.number('start') offset: number
  ): Promise<any> {


    try {



      const jwtClient = new google.auth.JWT(
        process.env.GOOGLE_CLIENT_EMAIL,
        '',
        process.env.GOOGLE_PRIVATE_KEY,
        ['https://www.googleapis.com/auth/admin.directory.user.readonly'],
      );

      const { access_token: token } = await jwtClient.authorize();
      console.log(token, ' our token')
      const response = await axios.get(
        'https://admin.googleapis.com/admin/directory/v1/users',
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );


      const usersResponse = response.data;
      // console.log(usersResponse)
      const users = await this.userRepository.find();
      const updatedUsers = usersResponse.data.value.map((user: { id: string; mail: string; }) => {
        const whiteListed = users.find(b => (b.email ?? '').trim().toLowerCase() === (user.mail ?? '').trim().toLowerCase());
        if (whiteListed) {
          return { ...user, country: whiteListed.country, status: 'active' };
        }
        return { ...user, country: '', status: 'inactive' };
      });
      return { data: updatedUsers, recordsTotal: usersResponse.data['@odata.count'] }
    }
    catch (e) {
      console.log(e)
    }



  }

  @get('/users', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })

  async find(): Promise<User[]> {
    return this.userRepository.find({ include: [{ relation: 'userLocation' }] });
  }

  @get('/users/external', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })

  async findExternalUser(): Promise<User[]> {
    return this.userRepository.find({ where: { type: 'External' } });
  }


  // 64a526ea9e7163b6c2e5ab3f
  @post('/users/incident-reviewer', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  // 
  async findIncidentReviewer(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              locationOneId: { type: 'string' },
              locationTwoId: { type: 'string' },
              locationThreeId: { type: 'string' },
              locationFourId: { type: 'string' },
            },
            required: ['locationOneId', 'locationTwoId', 'locationThreeId', 'locationFourId'],
          },
        },
      },
    })
    payload: {
      locationOneId: string;
      locationTwoId: string;
      locationThreeId: string;
      locationFourId: string;
    }
  ): Promise<User[]> {
    const roleId = "64a526ea9e7163b6c2e5ab3f";
    const allLocationId = 'all';

    let whereCondition = {
      roles: { inq: [[roleId]] },
      or: [
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: [payload.locationFourId] },
        },
        {
          locationOneId: { inq: ['tier1-all'] },
          locationTwoId: '',
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: ['tier2-all'] },
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: ['tier3-all'] },
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: ['tier4-all'] },
        },
      ]
    };

    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: whereCondition,
    });

    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    return users;
  }

  @get('/users/eptw-wah-assessor', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  // 
  async findWahAssessor(): Promise<User[]> {
    const roleId = "644b27e641cb57116200a88e";
    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: {
        roles: {
          inq: [[roleId]],
        },
      },
    });

    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    return users;
  }

  @get('/users/eptw-confined-assessor', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  // 
  async findConfinedAssessor(): Promise<User[]> {
    const roleId = "644b287241cb57116200a88f";
    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: {
        roles: {
          inq: [[roleId]],
        },
      },
    });

    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    return users;
  }

  @get('/users/eptw-lifting-assessor', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  // 
  async findLiftingAssessor(): Promise<User[]> {
    const roleId = "64741c0447efd939764b3f23";
    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: {
        roles: {
          inq: [[roleId]],
        },
      },
    });

    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    return users;
  }

  @get('/users/eptw-general-assessor', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  // 
  async findGeneralAssessor(): Promise<User[]> {
    const roleId = "64741c2347efd939764b3f24";
    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: {
        roles: {
          inq: [[roleId]],
        },
      },
    });

    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    return users;
  }


  @get('/users/eptw-high-risk-approver', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  // 
  async findHighRiskApprover(): Promise<User[]> {
    const roleId = "64a3db4b9e7163b6c2e5ab3b";
    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: {
        roles: {
          inq: [[roleId]],
        },
      },
    });

    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    return users;
  }

  @get('/users/eptw-dcso-approver', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  // 
  async findDcsoApprover(): Promise<User[]> {
    const roleId = "64a3db559e7163b6c2e5ab3c";
    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: {
        roles: {
          inq: [[roleId]],
        },
      },
    });

    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    return users;
  }


  @post('/users/eptw-dcso-approver', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  // 
  async findDcsoApproverPost(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              locationOneId: { type: 'string' },
              locationTwoId: { type: 'string' },
              locationThreeId: { type: 'string' },
              locationFourId: { type: 'string' },
            },
            required: ['locationOneId', 'locationTwoId', 'locationThreeId', 'locationFourId'],
          },
        },
      },
    })
    payload: {
      locationOneId: string;
      locationTwoId: string;
      locationThreeId: string;
      locationFourId: string;
    }
  ): Promise<User[]> {
    const roleId = "64a3db559e7163b6c2e5ab3c";
    const allLocationId = 'all';

    let whereCondition = {
      roles: { inq: [[roleId]] },
      or: [
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: [payload.locationFourId] },
        },
        {
          locationOneId: { inq: ['tier1-all'] },
          locationTwoId: '',
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: ['tier2-all'] },
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: ['tier3-all'] },
          locationFourId: '',
        },
        {
          locationOneId: { inq: [payload.locationOneId] },
          locationTwoId: { inq: [payload.locationTwoId] },
          locationThreeId: { inq: [payload.locationThreeId] },
          locationFourId: { inq: ['tier4-all'] },
        },
      ]
    };
    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: whereCondition,
    });

    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    return users;
  }


  @get('/users/eptw-dsco-assessor', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  // 
  async findDscoApprover(): Promise<User[]> {
    const roleId = "64ae72f1e5c20981cb2550b1";
    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: {
        roles: {
          inq: [[roleId]],
        },
      },
    });

    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    return users;
  }
  // 64ae72f1e5c20981cb2550b1

  @get('/users/me', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'The current user profile',
        content: {
          'application/json': {
            schema: UserProfileSchema,
          },
        },
      },
    },
  })

  async printCurrentUser(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
  ): Promise<any> {
    // (@jannyHou)FIXME: explore a way to generate OpenAPI schema
    // for symbol property

    // console.log(currentUserProfile)
    const email = currentUserProfile.email;
    // const user = await this.userRepository.findOne({ where: { email: email } });
    return this.userRepository.findOne({ where: { email: email }, include: [{ relation: 'userLocation' }] });

  }


  @post('/users/login', {
    responses: {
      '200': {
        description: 'Token',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                token: {
                  type: 'string',
                },
              },
            },
          },
        },
      },
    },
  })
  async login(
    @requestBody(CredentialsRequestBody) credentials: Credentials,
  ): Promise<{ token: string }> {
    // ensure the user exists, and the password is correct
    const user = await this.userService.verifyCredentials(credentials);

    // convert a User object into a UserProfile object (reduced set of properties)
    const userProfile = this.userService.convertToUserProfile(user);

    // create a JSON Web Token based on the user profile
    const token = await this.jwtService.generateToken(userProfile);

    return { token };
  }

  @put('/users/forgot-password', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'The updated user profile',
        content: {
          'application/json': {
            schema: UserProfileSchema,
          },
        },
      },
    },
  })

  async forgotPassword(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @requestBody(PasswordResetRequestBody) credentials: Credentials,
  ): Promise<{ token: string }> {
    const { email, password } = credentials;
    const { id } = currentUserProfile;

    const user = await this.userRepository.findById(id);

    if (!user) {
      throw new HttpErrors.NotFound('User account not found');
    }

    if (email !== user?.email) {
      throw new HttpErrors.Forbidden('Invalid email address');
    }

    validateCredentials(_.pick(credentials, ['email', 'password']));

    const passwordHash = await this.passwordHasher.hashPassword(password);

    await this.userRepository
      .userCredentials(user.id)
      .patch({ password: passwordHash });

    const userProfile = this.userService.convertToUserProfile(user);

    const token = await this.jwtService.generateToken(userProfile);

    return { token };
  }

  @post('/users/reset-password/init', {
    responses: {
      '200': {
        description: 'Confirmation that reset password email has been sent',
      },
    },
  })
  async resetPasswordInit(
    @requestBody() resetPasswordInit: ResetPasswordInit,
  ): Promise<string> {
    if (!isemail.validate(resetPasswordInit.email)) {
      throw new HttpErrors.UnprocessableEntity('Invalid email address');
    }

    const sentMessageInfo: SentMessageInfo =
      await this.userManagementService.requestPasswordReset(
        resetPasswordInit.email,
      );

    if (sentMessageInfo.accepted.length) {
      return 'Successfully sent reset password link';
    }
    throw new HttpErrors.InternalServerError(
      'Error sending reset password email',
    );
  }

  @put('/users/reset-password/finish', {
    responses: {
      '200': {
        description: 'A successful password reset response',
      },
    },
  })
  async resetPasswordFinish(
    @requestBody() keyAndPassword: KeyAndPassword,
  ): Promise<string> {
    validateKeyPassword(keyAndPassword);

    const foundUser = await this.userRepository.findOne({
      where: { resetKey: keyAndPassword.resetKey },
    });

    if (!foundUser) {
      throw new HttpErrors.NotFound(
        'No associated account for the provided reset key',
      );
    }

    const user = await this.userManagementService.validateResetKeyLifeSpan(
      foundUser,
    );

    const passwordHash = await this.passwordHasher.hashPassword(
      keyAndPassword.password,
    );

    try {
      await this.userRepository
        .userCredentials(user.id)
        .patch({ password: passwordHash });

      await this.userRepository.updateById(user.id, user);
    } catch (e) {
      return e;
    }

    return 'Password reset successful';
  }
}
