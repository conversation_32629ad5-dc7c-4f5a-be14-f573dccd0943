import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {LocationThree, LocationThreeRelations, LocationFour, UserLocationRole} from '../models';
import {LocationFourRepository} from './location-four.repository';
import {UserLocationRoleRepository} from './user-location-role.repository';

export class LocationThreeRepository extends DefaultCrudRepository<
  LocationThree,
  typeof LocationThree.prototype.id,
  LocationThreeRelations
> {

  public readonly locationFours: HasManyRepositoryFactory<LocationFour, typeof LocationThree.prototype.id>;

  public readonly userLocationRoles: HasManyRepositoryFactory<UserLocationRole, typeof LocationThree.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('LocationFourRepository') protected locationFourRepositoryGetter: Getter<LocationFourRepository>, @repository.getter('UserLocationRoleRepository') protected userLocationRoleRepositoryGetter: Getter<UserLocationRoleRepository>,
  ) {
    super(LocationThree, dataSource);
    this.userLocationRoles = this.createHasManyRepositoryFactoryFor('userLocationRoles', userLocationRoleRepositoryGetter,);
    this.registerInclusionResolver('userLocationRoles', this.userLocationRoles.inclusionResolver);
    this.locationFours = this.createHasManyRepositoryFactoryFor('locationFours', locationFourRepositoryGetter,);
    this.registerInclusionResolver('locationFours', this.locationFours.inclusionResolver);
  }
}
