import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {IncidentCircumstanceType} from '../models';
import {IncidentCircumstanceTypeRepository} from '../repositories';

export class IncidentCircumstanceTypeController {
  constructor(
    @repository(IncidentCircumstanceTypeRepository)
    public incidentCircumstanceTypeRepository : IncidentCircumstanceTypeRepository,
  ) {}

  @post('/incident-circumstance-types')
  @response(200, {
    description: 'IncidentCircumstanceType model instance',
    content: {'application/json': {schema: getModelSchemaRef(IncidentCircumstanceType)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(IncidentCircumstanceType, {
            title: 'NewIncidentCircumstanceType',
            exclude: ['id'],
          }),
        },
      },
    })
    incidentCircumstanceType: Omit<IncidentCircumstanceType, 'id'>,
  ): Promise<IncidentCircumstanceType> {
    return this.incidentCircumstanceTypeRepository.create(incidentCircumstanceType);
  }

  @get('/incident-circumstance-types/count')
  @response(200, {
    description: 'IncidentCircumstanceType model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(IncidentCircumstanceType) where?: Where<IncidentCircumstanceType>,
  ): Promise<Count> {
    return this.incidentCircumstanceTypeRepository.count(where);
  }

  @get('/incident-circumstance-types')
  @response(200, {
    description: 'Array of IncidentCircumstanceType model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(IncidentCircumstanceType, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(IncidentCircumstanceType) filter?: Filter<IncidentCircumstanceType>,
  ): Promise<IncidentCircumstanceType[]> {
    return this.incidentCircumstanceTypeRepository.find(filter);
  }

  @patch('/incident-circumstance-types')
  @response(200, {
    description: 'IncidentCircumstanceType PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(IncidentCircumstanceType, {partial: true}),
        },
      },
    })
    incidentCircumstanceType: IncidentCircumstanceType,
    @param.where(IncidentCircumstanceType) where?: Where<IncidentCircumstanceType>,
  ): Promise<Count> {
    return this.incidentCircumstanceTypeRepository.updateAll(incidentCircumstanceType, where);
  }

  @get('/incident-circumstance-types/{id}')
  @response(200, {
    description: 'IncidentCircumstanceType model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(IncidentCircumstanceType, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(IncidentCircumstanceType, {exclude: 'where'}) filter?: FilterExcludingWhere<IncidentCircumstanceType>
  ): Promise<IncidentCircumstanceType> {
    return this.incidentCircumstanceTypeRepository.findById(id, filter);
  }

  @patch('/incident-circumstance-types/{id}')
  @response(204, {
    description: 'IncidentCircumstanceType PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(IncidentCircumstanceType, {partial: true}),
        },
      },
    })
    incidentCircumstanceType: IncidentCircumstanceType,
  ): Promise<void> {
    await this.incidentCircumstanceTypeRepository.updateById(id, incidentCircumstanceType);
  }

  @put('/incident-circumstance-types/{id}')
  @response(204, {
    description: 'IncidentCircumstanceType PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() incidentCircumstanceType: IncidentCircumstanceType,
  ): Promise<void> {
    await this.incidentCircumstanceTypeRepository.replaceById(id, incidentCircumstanceType);
  }

  @del('/incident-circumstance-types/{id}')
  @response(204, {
    description: 'IncidentCircumstanceType DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.incidentCircumstanceTypeRepository.deleteById(id);
  }
}
