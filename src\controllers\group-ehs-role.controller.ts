import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {GroupEhsRole} from '../models';
import {GroupEhsRoleRepository} from '../repositories';

export class GroupEhsRoleController {
  constructor(
    @repository(GroupEhsRoleRepository)
    public groupEhsRoleRepository : GroupEhsRoleRepository,
  ) {}

  @post('/group-ehs-roles')
  @response(200, {
    description: 'GroupEhsRole model instance',
    content: {'application/json': {schema: getModelSchemaRef(GroupEhsRole)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GroupEhsRole, {
            title: 'NewGroupEhsRole',
            exclude: ['id'],
          }),
        },
      },
    })
    groupEhsRole: Omit<GroupEhsRole, 'id'>,
  ): Promise<GroupEhsRole> {
    return this.groupEhsRoleRepository.create(groupEhsRole);
  }

  @get('/group-ehs-roles/count')
  @response(200, {
    description: 'GroupEhsRole model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(GroupEhsRole) where?: Where<GroupEhsRole>,
  ): Promise<Count> {
    return this.groupEhsRoleRepository.count(where);
  }

  @get('/group-ehs-roles')
  @response(200, {
    description: 'Array of GroupEhsRole model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(GroupEhsRole, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(GroupEhsRole) filter?: Filter<GroupEhsRole>,
  ): Promise<GroupEhsRole[]> {
    return this.groupEhsRoleRepository.find(filter);
  }

  @patch('/group-ehs-roles')
  @response(200, {
    description: 'GroupEhsRole PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GroupEhsRole, {partial: true}),
        },
      },
    })
    groupEhsRole: GroupEhsRole,
    @param.where(GroupEhsRole) where?: Where<GroupEhsRole>,
  ): Promise<Count> {
    return this.groupEhsRoleRepository.updateAll(groupEhsRole, where);
  }

  @get('/group-ehs-roles/{id}')
  @response(200, {
    description: 'GroupEhsRole model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(GroupEhsRole, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(GroupEhsRole, {exclude: 'where'}) filter?: FilterExcludingWhere<GroupEhsRole>
  ): Promise<GroupEhsRole> {
    return this.groupEhsRoleRepository.findById(id, filter);
  }

  @patch('/group-ehs-roles/{id}')
  @response(204, {
    description: 'GroupEhsRole PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GroupEhsRole, {partial: true}),
        },
      },
    })
    groupEhsRole: GroupEhsRole,
  ): Promise<void> {
    await this.groupEhsRoleRepository.updateById(id, groupEhsRole);
  }

  @put('/group-ehs-roles/{id}')
  @response(204, {
    description: 'GroupEhsRole PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() groupEhsRole: GroupEhsRole,
  ): Promise<void> {
    await this.groupEhsRoleRepository.replaceById(id, groupEhsRole);
  }

  @del('/group-ehs-roles/{id}')
  @response(204, {
    description: 'GroupEhsRole DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.groupEhsRoleRepository.deleteById(id);
  }
}
