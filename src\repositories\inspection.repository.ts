import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {Inspection, InspectionRelations, User, Checklist, LocationOne, LocationTwo, LocationThree, LocationFour} from '../models';
import {UserRepository} from './user.repository';
import {ChecklistRepository} from './checklist.repository';
import {LocationOneRepository} from './location-one.repository';
import {LocationTwoRepository} from './location-two.repository';
import {LocationThreeRepository} from './location-three.repository';
import {LocationFourRepository} from './location-four.repository';

export class InspectionRepository extends DefaultCrudRepository<
  Inspection,
  typeof Inspection.prototype.id,
  InspectionRelations
> {

  public readonly approver: BelongsToAccessor<User, typeof Inspection.prototype.id>;

  public readonly assignedBy: BelongsToAccessor<User, typeof Inspection.prototype.id>;

  public readonly checklist: BelongsToAccessor<Checklist, typeof Inspection.prototype.id>;

  public readonly assignedTo: BelongsToAccessor<User, typeof Inspection.prototype.id>;

  public readonly locationOne: BelongsToAccessor<LocationOne, typeof Inspection.prototype.id>;

  public readonly locationTwo: BelongsToAccessor<LocationTwo, typeof Inspection.prototype.id>;

  public readonly locationThree: BelongsToAccessor<LocationThree, typeof Inspection.prototype.id>;

  public readonly locationFour: BelongsToAccessor<LocationFour, typeof Inspection.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('UserRepository') protected userRepositoryGetter: Getter<UserRepository>, @repository.getter('ChecklistRepository') protected checklistRepositoryGetter: Getter<ChecklistRepository>, @repository.getter('LocationOneRepository') protected locationOneRepositoryGetter: Getter<LocationOneRepository>, @repository.getter('LocationTwoRepository') protected locationTwoRepositoryGetter: Getter<LocationTwoRepository>, @repository.getter('LocationThreeRepository') protected locationThreeRepositoryGetter: Getter<LocationThreeRepository>, @repository.getter('LocationFourRepository') protected locationFourRepositoryGetter: Getter<LocationFourRepository>,
  ) {
    super(Inspection, dataSource);
    this.locationFour = this.createBelongsToAccessorFor('locationFour', locationFourRepositoryGetter,);
    this.registerInclusionResolver('locationFour', this.locationFour.inclusionResolver);
    this.locationThree = this.createBelongsToAccessorFor('locationThree', locationThreeRepositoryGetter,);
    this.registerInclusionResolver('locationThree', this.locationThree.inclusionResolver);
    this.locationTwo = this.createBelongsToAccessorFor('locationTwo', locationTwoRepositoryGetter,);
    this.registerInclusionResolver('locationTwo', this.locationTwo.inclusionResolver);
    this.locationOne = this.createBelongsToAccessorFor('locationOne', locationOneRepositoryGetter,);
    this.registerInclusionResolver('locationOne', this.locationOne.inclusionResolver);
    this.assignedTo = this.createBelongsToAccessorFor('assignedTo', userRepositoryGetter,);
    this.registerInclusionResolver('assignedTo', this.assignedTo.inclusionResolver);
    this.checklist = this.createBelongsToAccessorFor('checklist', checklistRepositoryGetter,);
    this.registerInclusionResolver('checklist', this.checklist.inclusionResolver);
    this.assignedBy = this.createBelongsToAccessorFor('assignedBy', userRepositoryGetter,);
    this.registerInclusionResolver('assignedBy', this.assignedBy.inclusionResolver);
    this.approver = this.createBelongsToAccessorFor('approver', userRepositoryGetter,);
    this.registerInclusionResolver('approver', this.approver.inclusionResolver);
  }
}
