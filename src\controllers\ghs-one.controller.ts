import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {GhsOne} from '../models';
import {GhsOneRepository} from '../repositories';
import { authenticate } from '@loopback/authentication';
@authenticate('jwt')
export class GhsOneController {
  constructor(
    @repository(GhsOneRepository)
    public ghsOneRepository : GhsOneRepository,
  ) {}

  @post('/ghs-ones')
  @response(200, {
    description: 'GhsOne model instance',
    content: {'application/json': {schema: getModelSchemaRef(GhsOne)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GhsOne, {
            title: 'NewGhsOne',
            exclude: ['id'],
          }),
        },
      },
    })
    ghsOne: Omit<GhsOne, 'id'>,
  ): Promise<GhsOne> {
    return this.ghsOneRepository.create(ghsOne);
  }

  @get('/ghs-ones/count')
  @response(200, {
    description: 'GhsOne model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(GhsOne) where?: Where<GhsOne>,
  ): Promise<Count> {
    return this.ghsOneRepository.count(where);
  }

  @get('/ghs-ones')
  @response(200, {
    description: 'Array of GhsOne model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(GhsOne, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(GhsOne) filter?: Filter<GhsOne>,
  ): Promise<GhsOne[]> {
    return this.ghsOneRepository.find(filter);
  }

  @patch('/ghs-ones')
  @response(200, {
    description: 'GhsOne PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GhsOne, {partial: true}),
        },
      },
    })
    ghsOne: GhsOne,
    @param.where(GhsOne) where?: Where<GhsOne>,
  ): Promise<Count> {
    return this.ghsOneRepository.updateAll(ghsOne, where);
  }

  @get('/ghs-ones/{id}')
  @response(200, {
    description: 'GhsOne model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(GhsOne, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(GhsOne, {exclude: 'where'}) filter?: FilterExcludingWhere<GhsOne>
  ): Promise<GhsOne> {
    return this.ghsOneRepository.findById(id, filter);
  }

  @patch('/ghs-ones/{id}')
  @response(204, {
    description: 'GhsOne PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GhsOne, {partial: true}),
        },
      },
    })
    ghsOne: GhsOne,
  ): Promise<void> {
    await this.ghsOneRepository.updateById(id, ghsOne);
  }

  @put('/ghs-ones/{id}')
  @response(204, {
    description: 'GhsOne PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() ghsOne: GhsOne,
  ): Promise<void> {
    await this.ghsOneRepository.replaceById(id, ghsOne);
  }

  @del('/ghs-ones/{id}')
  @response(204, {
    description: 'GhsOne DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.ghsOneRepository.deleteById(id);
  }
}
