import {Entity, model, property} from '@loopback/repository';

@model()
export class CustomName extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  key?: string;

  @property({
    type: 'string',
  })
  value?: string;

  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'string',
  })
  modified?: string;


  constructor(data?: Partial<CustomName>) {
    super(data);
  }
}

export interface CustomNameRelations {
  // describe navigational properties here
}

export type CustomNameWithRelations = CustomName & CustomNameRelations;
