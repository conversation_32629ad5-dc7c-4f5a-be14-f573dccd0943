import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {SurfaceType} from '../models';
import {SurfaceTypeRepository} from '../repositories';

export class SurfaceTypeController {
  constructor(
    @repository(SurfaceTypeRepository)
    public surfaceTypeRepository : SurfaceTypeRepository,
  ) {}

  @post('/surface-types')
  @response(200, {
    description: 'SurfaceType model instance',
    content: {'application/json': {schema: getModelSchemaRef(SurfaceType)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SurfaceType, {
            title: 'NewSurfaceType',
            exclude: ['id'],
          }),
        },
      },
    })
    surfaceType: Omit<SurfaceType, 'id'>,
  ): Promise<SurfaceType> {
    return this.surfaceTypeRepository.create(surfaceType);
  }

  @get('/surface-types/count')
  @response(200, {
    description: 'SurfaceType model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(SurfaceType) where?: Where<SurfaceType>,
  ): Promise<Count> {
    return this.surfaceTypeRepository.count(where);
  }

  @get('/surface-types')
  @response(200, {
    description: 'Array of SurfaceType model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(SurfaceType, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(SurfaceType) filter?: Filter<SurfaceType>,
  ): Promise<SurfaceType[]> {
    return this.surfaceTypeRepository.find(filter);
  }

  @patch('/surface-types')
  @response(200, {
    description: 'SurfaceType PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SurfaceType, {partial: true}),
        },
      },
    })
    surfaceType: SurfaceType,
    @param.where(SurfaceType) where?: Where<SurfaceType>,
  ): Promise<Count> {
    return this.surfaceTypeRepository.updateAll(surfaceType, where);
  }

  @get('/surface-types/{id}')
  @response(200, {
    description: 'SurfaceType model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(SurfaceType, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(SurfaceType, {exclude: 'where'}) filter?: FilterExcludingWhere<SurfaceType>
  ): Promise<SurfaceType> {
    return this.surfaceTypeRepository.findById(id, filter);
  }

  @patch('/surface-types/{id}')
  @response(204, {
    description: 'SurfaceType PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SurfaceType, {partial: true}),
        },
      },
    })
    surfaceType: SurfaceType,
  ): Promise<void> {
    await this.surfaceTypeRepository.updateById(id, surfaceType);
  }

  @put('/surface-types/{id}')
  @response(204, {
    description: 'SurfaceType PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() surfaceType: SurfaceType,
  ): Promise<void> {
    await this.surfaceTypeRepository.replaceById(id, surfaceType);
  }

  @del('/surface-types/{id}')
  @response(204, {
    description: 'SurfaceType DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.surfaceTypeRepository.deleteById(id);
  }
}
