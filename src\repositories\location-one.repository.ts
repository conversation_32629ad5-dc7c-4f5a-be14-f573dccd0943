import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {LocationOne, LocationOneRelations, LocationTwo, UserLocationRole} from '../models';
import {LocationTwoRepository} from './location-two.repository';
import {UserLocationRoleRepository} from './user-location-role.repository';

export class LocationOneRepository extends DefaultCrudRepository<
  LocationOne,
  typeof LocationOne.prototype.id,
  LocationOneRelations
> {

  public readonly locationTwos: HasManyRepositoryFactory<LocationTwo, typeof LocationOne.prototype.id>;

  public readonly userLocationRoles: HasManyRepositoryFactory<UserLocationRole, typeof LocationOne.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('LocationTwoRepository') protected locationTwoRepositoryGetter: Getter<LocationTwoRepository>, @repository.getter('UserLocationRoleRepository') protected userLocationRoleRepositoryGetter: Getter<UserLocationRoleRepository>,
  ) {
    super(LocationOne, dataSource);
    this.userLocationRoles = this.createHasManyRepositoryFactoryFor('userLocationRoles', userLocationRoleRepositoryGetter,);
    this.registerInclusionResolver('userLocationRoles', this.userLocationRoles.inclusionResolver);
    this.locationTwos = this.createHasManyRepositoryFactoryFor('locationTwos', locationTwoRepositoryGetter,);
    this.registerInclusionResolver('locationTwos', this.locationTwos.inclusionResolver);
  }
}
