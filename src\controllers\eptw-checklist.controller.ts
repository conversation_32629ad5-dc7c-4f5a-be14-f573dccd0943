import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import { EptwChecklist } from '../models';
import { EptwChecklistRepository } from '../repositories';

export class EptwChecklistController {
  constructor(
    @repository(EptwChecklistRepository)
    public eptwChecklistRepository: EptwChecklistRepository,
  ) { }

  @post('/eptw-checklists')
  @response(200, {
    description: 'EptwChecklist model instance',
    content: { 'application/json': { schema: getModelSchemaRef(EptwChecklist) } },
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(EptwChecklist, {
            title: 'NewEptwChecklist',
            exclude: ['id'],
          }),
        },
      },
    })
    eptwChecklist: Omit<EptwChecklist, 'id'>,
  ): Promise<EptwChecklist> {
    const items = await this.eptwChecklistRepository.count();
    const maxId = items.count;
    const newId = maxId + 1;

    const newChecklist = { id: newId, ...eptwChecklist };
    return this.eptwChecklistRepository.create(newChecklist);
  }

  @get('/eptw-checklists/count')
  @response(200, {
    description: 'EptwChecklist model count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async count(
    @param.where(EptwChecklist) where?: Where<EptwChecklist>,
  ): Promise<Count> {
    return this.eptwChecklistRepository.count(where);
  }

  @get('/eptw-checklists')
  @response(200, {
    description: 'Array of EptwChecklist model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(EptwChecklist, { includeRelations: true }),
        },
      },
    },
  })
  async find(
    @param.filter(EptwChecklist) filter?: Filter<EptwChecklist>,
  ): Promise<EptwChecklist[]> {
    return this.eptwChecklistRepository.find(filter);
  }

  @patch('/eptw-checklists')
  @response(200, {
    description: 'EptwChecklist PATCH success count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(EptwChecklist, { partial: true }),
        },
      },
    })
    eptwChecklist: EptwChecklist,
    @param.where(EptwChecklist) where?: Where<EptwChecklist>,
  ): Promise<Count> {
    return this.eptwChecklistRepository.updateAll(eptwChecklist, where);
  }

  @get('/eptw-checklists/{id}')
  @response(200, {
    description: 'EptwChecklist model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(EptwChecklist, { includeRelations: true }),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(EptwChecklist, { exclude: 'where' }) filter?: FilterExcludingWhere<EptwChecklist>
  ): Promise<EptwChecklist> {
    return this.eptwChecklistRepository.findById(id, filter);
  }

  @patch('/eptw-checklists/{id}')
  @response(204, {
    description: 'EptwChecklist PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(EptwChecklist, { partial: true }),
        },
      },
    })
    eptwChecklist: EptwChecklist,
  ): Promise<void> {
    await this.eptwChecklistRepository.updateById(id, eptwChecklist);
  }

  @put('/eptw-checklists/{id}')
  @response(204, {
    description: 'EptwChecklist PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() eptwChecklist: EptwChecklist,
  ): Promise<void> {
    await this.eptwChecklistRepository.replaceById(id, eptwChecklist);
  }

  @del('/eptw-checklists/{id}')
  @response(204, {
    description: 'EptwChecklist DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.eptwChecklistRepository.deleteById(id);
  }
}
