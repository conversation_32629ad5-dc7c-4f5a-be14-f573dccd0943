import {Entity, model, property} from '@loopback/repository';

@model()
export class AuditRole extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'any',
  })
  permission?: any;

  @property({
    type: 'number',
  })
  order?: number;
  
  @property({
    type: 'date',
  })
  created?: string;
  
  constructor(data?: Partial<AuditRole>) {
    super(data);
  }
}

export interface AuditRoleRelations {
  // describe navigational properties here
}

export type AuditRoleWithRelations = AuditRole & AuditRoleRelations;
