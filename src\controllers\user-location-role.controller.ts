import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import { inject } from '@loopback/core';
import { SecurityBindings, securityId, UserProfile } from '@loopback/security';
import { UserLocationRole, User } from '../models';
import { UserLocationRoleRepository, UserRepository } from '../repositories';
import { v4 as uuidv4 } from 'uuid';

export interface UserLocationRoleWithDisabledRoles extends UserLocationRole {
  disabledRoles?: string[];
}
import { authenticate } from '@loopback/authentication';
@authenticate('jwt')
export class UserLocationRoleController {
  constructor(
    @repository(UserLocationRoleRepository)
    public userLocationRoleRepository: UserLocationRoleRepository,
    @repository(UserRepository)
    public userRepository: UserRepository,
  ) { }

  @post('/user-location-roles')
  @response(200, {
    description: 'UserLocationRole model instance',
    content: { 'application/json': { schema: getModelSchemaRef(UserLocationRole) } },
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              userIds: {
                type: 'array',
                items: {
                  type: 'string',
                },
              },
              deselectUserIds: {
                type: 'array',
                items: {
                  type: 'string',
                },
              },
              roles: {
                type: 'array',
                items: {
                  type: 'string',
                },
              },
              locations: {
                type: 'object',
                properties: {
                  locationOne: {
                    type: 'string',
                  },
                  locationTwo: {
                    type: 'string',
                  },
                  locationThree: {
                    type: 'string',
                  },
                  locationFour: {
                    type: 'string',

                  }
                  // Repeat the pattern for locationThree, locationFour, locationFive, and locationSix
                },
              },
            },
          },
        },
      },
    })
    requestBody: { userIds: string[], deselectUserIds: string[], roles: string[], locations: { [key: string]: string } },
  ): Promise<any> {
    console.log(requestBody)
    // return this.userLocationRoleRepository.create(userLocationRole);
    const { userIds, deselectUserIds, roles, locations } = requestBody;
    const savedUserLocationRoles: UserLocationRole[] = [];
    for (const userId of userIds) {
      const existingUserLocationRole = await this.userLocationRoleRepository.findOne({
        where: {
          userId: userId,
          locationOneId: locations.locationOne,
          locationTwoId: locations.locationTwo,
          locationThreeId: locations.locationThree,
          locationFourId: locations.locationFour,
        },
      });
      if (existingUserLocationRole) {

        if (existingUserLocationRole.roles) {
          const combinedRoles = [...new Set(existingUserLocationRole.roles.concat(roles))];
          existingUserLocationRole.roles = combinedRoles;
        }

        await this.userLocationRoleRepository.updateById(existingUserLocationRole.id, existingUserLocationRole);
        savedUserLocationRoles.push(existingUserLocationRole);
      } else {
        const userLocationRole = new UserLocationRole();
        userLocationRole.userId = userId;
        userLocationRole.roles = roles;
        userLocationRole.locationOneId = locations.locationOne;
        userLocationRole.locationTwoId = locations.locationTwo;
        userLocationRole.locationThreeId = locations.locationThree;
        userLocationRole.locationFourId = locations.locationFour;
        const savedUserLocationRole = await this.userLocationRoleRepository.create(userLocationRole);
        savedUserLocationRoles.push(savedUserLocationRole);
      }
    }

    for (const userId of deselectUserIds) {
      const existingUserLocationRole = await this.userLocationRoleRepository.findOne({
        where: {
          userId: userId,
          locationOneId: locations.locationOne,
          locationTwoId: locations.locationTwo,
          locationThreeId: locations.locationThree,
          locationFourId: locations.locationFour,
        },
      });

      if (existingUserLocationRole) {

        if (existingUserLocationRole.roles) {
          const filteredRoles = existingUserLocationRole.roles.filter((role) => !roles.includes(role));
          existingUserLocationRole.roles = filteredRoles;
        }

        await this.userLocationRoleRepository.updateById(existingUserLocationRole.id, existingUserLocationRole);
        savedUserLocationRoles.push(existingUserLocationRole);
      }
    }

    return savedUserLocationRoles;
  }
  @post('/user-location-roles/get-users')
  @response(200, {
    description: 'List of users under the specified locations with roles',
    content: { 'application/json': { schema: { type: 'array', items: { 'x-ts-type': User } } } },
  })
  async getUsers(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              roles: {
                type: 'array',
                items: {
                  type: 'string',
                },
              },
              locations: {
                type: 'object',
                properties: {
                  locationOne: { type: 'string' },
                  locationTwo: { type: 'string' },
                  locationThree: { type: 'string' },
                  locationFour: { type: 'string' },
                },
              },
            },
          },
        },
      },
    })
    requestBody: { roles: string[], locations: { [key: string]: string } },
  ): Promise<User[]> {
    console.log(requestBody);

    const { roles, locations } = requestBody;
    const { locationOne, locationTwo, locationThree, locationFour } = locations;

    const allLocationId = 'all';

    let whereCondition = {
      roles: { inq: [roles] },
      or: [
        {
          locationOneId: { inq: [locationOne] },
          locationTwoId: { inq: [locationTwo] },
          locationThreeId: { inq: [locationThree] },
          locationFourId: { inq: [locationFour] },
        },
        {
          locationOneId: { inq: ['tier1-all'] },
          locationTwoId: '',
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [locationOne] },
          locationTwoId: { inq: ['tier2-all'] },
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: { inq: [locationOne] },
          locationTwoId: { inq: [locationTwo] },
          locationThreeId: { inq: ['tier3-all'] },
          locationFourId: '',
        },
        {
          locationOneId: { inq: [locationOne] },
          locationTwoId: { inq: [locationTwo] },
          locationThreeId: { inq: [locationThree] },
          locationFourId: { inq: ['tier4-all'] },
        },
      ]
    };

    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: whereCondition,
    });

    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: { id: { inq: uniqueUserIds as string[] } },
    });

    return users;
  }

  // @post('/user-location-roles/get-users')
  // @response(200, {
  //   description: 'List of users under the specified locations with roles',
  //   content: { 'application/json': { schema: { type: 'array', items: { 'x-ts-type': User } } } },
  // })
  // async getUsers(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: {
  //           type: 'object',
  //           properties: {
  //             roles: {
  //               type: 'array',
  //               items: {
  //                 type: 'string',
  //               },
  //             },
  //             locations: {
  //               type: 'object',
  //               properties: {
  //                 locationOne: {
  //                   type: 'string',
  //                 },
  //                 locationTwo: {
  //                   type: 'string',
  //                 },
  //                 locationThree: {
  //                   type: 'string',
  //                 },
  //                 locationFour: {
  //                   type: 'string',
  //                 },
  //               },
  //             },
  //           },
  //         },
  //       },
  //     },
  //   })
  //   requestBody: { roles: string[], locations: { [key: string]: string } },
  // ): Promise<User[]> {
  //   console.log(requestBody);

  //   const { roles, locations } = requestBody;

  //   const locationConditions = Object.entries(locations).map(([key, value]) => ({ [`${key}Id`]: value }));


  //   const userLocationRoles = await this.userLocationRoleRepository.find({
  //     where: {
  //       and: locationConditions


  //     },
  //   });



  //   const filteredUserLocationRoles = userLocationRoles.filter((userLocationRole) => {
  //     if (userLocationRole.roles)
  //       return userLocationRole.roles.some((role) => roles.includes(role));
  //   });

  //   const userIds = filteredUserLocationRoles.map(i => i.userId);

  //   const users = await this.userRepository.find({
  //     where: { id: { inq: userIds as string[] } },
  //   });

  //   return users;
  // }


  @post('/individual-user-location-roles')
  @response(200, {
    description: 'IndividualUserLocationRole model instance',
    content: { 'application/json': { schema: getModelSchemaRef(UserLocationRole) } },
  })
  async createIndividual(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              userId: {
                type: 'string',
              },
              roles: {
                type: 'array',
                items: {
                  type: 'string',
                },
              },
              locations: {
                type: 'object',
                properties: {
                  locationOne: {
                    type: 'string',
                  },
                  locationTwo: {
                    type: 'string',
                  },
                  locationThree: {
                    type: 'string',
                  },
                  locationFour: {
                    type: 'string',

                  }
                  // Repeat the pattern for locationThree, locationFour, locationFive, and locationSix
                },
              },
            },
          },
        },
      },
    })
    requestBody: { userId: string, roles: string[], locations: { [key: string]: string } },
  ): Promise<any> {
    console.log(requestBody)
    // return this.userLocationRoleRepository.create(userLocationRole);
    const { userId, roles, locations } = requestBody;
    const savedUserLocationRoles: UserLocationRole[] = [];

    const existingUserLocationRole = await this.userLocationRoleRepository.findOne({
      where: {
        userId: userId,
        locationOneId: locations.locationOne,
        locationTwoId: locations.locationTwo,
        locationThreeId: locations.locationThree,
        locationFourId: locations.locationFour,
      },
    });
    if (existingUserLocationRole) {

      if (existingUserLocationRole.roles) {

        existingUserLocationRole.roles = roles;
      }

      await this.userLocationRoleRepository.updateById(existingUserLocationRole.id, existingUserLocationRole);
      savedUserLocationRoles.push(existingUserLocationRole);
    } else {
      const userLocationRole = new UserLocationRole();
      userLocationRole.userId = userId;
      userLocationRole.roles = roles;
      userLocationRole.locationOneId = locations.locationOne;
      userLocationRole.locationTwoId = locations.locationTwo;
      userLocationRole.locationThreeId = locations.locationThree;
      userLocationRole.locationFourId = locations.locationFour;
      const savedUserLocationRole = await this.userLocationRoleRepository.create(userLocationRole);
      savedUserLocationRoles.push(savedUserLocationRole);
    }


    return savedUserLocationRoles;
  }

  @authenticate.skip()
  @post('/user-location-roles/get-individual-users')
  @response(200, {
    description: 'List of users under the specified locations with roles',
    content: { 'application/json': { schema: { type: 'array', items: { 'x-ts-type': User } } } },
  })
  async getIndividualUsers(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {

              userId: {
                type: 'string'
              },
              locations: {
                type: 'object',
                properties: {
                  locationOne: {
                    type: 'string',
                  },
                  locationTwo: {
                    type: 'string',
                  },
                  locationThree: {
                    type: 'string',
                  },
                  locationFour: {
                    type: 'string',
                  },
                },
              },
            },
          },
        },
      },
    })
    requestBody: { roles: string[], userId: string, locations: { [key: string]: string } },
  ): Promise<UserLocationRoleWithDisabledRoles[]> {
    console.log(requestBody);

    const { roles, userId, locations } = requestBody;
    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: {
        and: [
          { locationOneId: locations.locationOne },
          { locationTwoId: locations.locationTwo },
          { locationThreeId: locations.locationThree },
          { locationFourId: locations.locationFour },
          { userId: userId }

        ]


      },
    });

    let disabledRoles: string[] = [];

    if (locations.locationOne === 'tier1-all') {

    }

    if (locations.locationTwo === 'tier2-all' || locations.locationTwo) {
      const allLocationOneRoles = await this.userLocationRoleRepository.findOne({
        where: {
          and: [
            { locationOneId: 'tier1-all' },
            { userId: userId }
          ]
        }
      });

      disabledRoles.push(...allLocationOneRoles?.roles || [])
    }

    if (locations.locationThree === 'tier3-all' || locations.locationThree) {
      const allLocationTwoRoles = await this.userLocationRoleRepository.findOne({
        where: {
          and: [
            { locationTwoId: 'tier2-all' },
            { userId: userId }
          ]
        }
      });

      disabledRoles.push(...allLocationTwoRoles?.roles || [])
    }

    if (locations.locationFour === 'tier4-all' || locations.locationFour) {
      const allLocationThreeRoles = await this.userLocationRoleRepository.findOne({
        where: {
          and: [
            { locationThreeId: 'tier3-all' },
            { userId: userId }
          ]
        }
      });

      disabledRoles.push(...allLocationThreeRoles?.roles || [])
    }

    if (locations.locationFour !== 'tier4-all' && locations.locationFour) {
      const allLocationFourRoles = await this.userLocationRoleRepository.findOne({
        where: {
          and: [
            { locationThreeId: locations.locationThree },
            { locationFourId: 'tier4-all' },
            { userId: userId }
          ]
        }
      });

      disabledRoles.push(...allLocationFourRoles?.roles || [])
    }

    disabledRoles = [...new Set(disabledRoles)];

    if (userLocationRoles.length === 0) {
      const newUserLocationRole: UserLocationRoleWithDisabledRoles = {
        roles: [],
        disabledRoles: disabledRoles,
        getId: () => { return uuidv4(); },
        getIdObject: () => { return { roles: [], disabledRoles: disabledRoles }; },
        toJSON: () => { return { roles: [], disabledRoles: disabledRoles }; },
        toObject: () => { return { roles: [], disabledRoles: disabledRoles }; },
        // add any other properties required by the UserLocationRoleWithDisabledRoles type here
      };
      return [newUserLocationRole];
    } else {
      const userLocationRolesList = userLocationRoles.map(i => { return { ...i, disabledRoles: disabledRoles } });
      return userLocationRolesList as UserLocationRoleWithDisabledRoles[];
    }

  }

  @get('/user-location-roles/count')
  @response(200, {
    description: 'UserLocationRole model count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async count(
    @param.where(UserLocationRole) where?: Where<UserLocationRole>,
  ): Promise<Count> {
    return this.userLocationRoleRepository.count(where);
  }

  @get('/user-location-roles')
  @response(200, {
    description: 'Array of UserLocationRole model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(UserLocationRole, { includeRelations: true }),
        },
      },
    },
  })
  async find(
    @param.filter(UserLocationRole) filter?: Filter<UserLocationRole>,
  ): Promise<UserLocationRole[]> {
    return this.userLocationRoleRepository.find(filter);
  }

  @authenticate.skip()
  @get('/my-user-location-roles/{id}')
  @response(200, {
    description: 'Array of UserLocationRole model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(UserLocationRole, { includeRelations: true }),
        },
      },
    },
  })
  async findMy(
    @param.path.string('id') id: string,
    @param.filter(UserLocationRole) filter?: Filter<UserLocationRole>,
  ): Promise<UserLocationRole[]> {

    return this.userLocationRoleRepository.find({ where: { userId: id } });
  }


  @patch('/user-location-roles')
  @response(200, {
    description: 'UserLocationRole PATCH success count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(UserLocationRole, { partial: true }),
        },
      },
    })
    userLocationRole: UserLocationRole,
    @param.where(UserLocationRole) where?: Where<UserLocationRole>,
  ): Promise<Count> {
    return this.userLocationRoleRepository.updateAll(userLocationRole, where);
  }

  @get('/user-location-roles/{id}')
  @response(200, {
    description: 'UserLocationRole model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(UserLocationRole, { includeRelations: true }),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(UserLocationRole, { exclude: 'where' }) filter?: FilterExcludingWhere<UserLocationRole>
  ): Promise<UserLocationRole> {
    return this.userLocationRoleRepository.findById(id, filter);
  }

  @patch('/user-location-roles/{id}')
  @response(204, {
    description: 'UserLocationRole PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(UserLocationRole, { partial: true }),
        },
      },
    })
    userLocationRole: UserLocationRole,
  ): Promise<void> {
    await this.userLocationRoleRepository.updateById(id, userLocationRole);
  }

  @put('/user-location-roles/{id}')
  @response(204, {
    description: 'UserLocationRole PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() userLocationRole: UserLocationRole,
  ): Promise<void> {
    await this.userLocationRoleRepository.replaceById(id, userLocationRole);
  }

  @del('/user-location-roles/{id}')
  @response(204, {
    description: 'UserLocationRole DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.userLocationRoleRepository.deleteAll({ userId: id });
  }
}
