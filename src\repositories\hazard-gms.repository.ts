import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {HazardGms, HazardGmsRelations} from '../models';

export class HazardGmsRepository extends DefaultCrudRepository<
  HazardGms,
  typeof HazardGms.prototype.id,
  HazardGmsRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(HazardGms, dataSource);
  }
}
