import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {IncidentRootCauseDescription} from '../models';
import {IncidentRootCauseDescriptionRepository} from '../repositories';

export class IncidentRootCauseDescriptionController {
  constructor(
    @repository(IncidentRootCauseDescriptionRepository)
    public incidentRootCauseDescriptionRepository : IncidentRootCauseDescriptionRepository,
  ) {}

  @post('/incident-root-cause-descriptions')
  @response(200, {
    description: 'IncidentRootCauseDescription model instance',
    content: {'application/json': {schema: getModelSchemaRef(IncidentRootCauseDescription)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(IncidentRootCauseDescription, {
            title: 'NewIncidentRootCauseDescription',
            exclude: ['id'],
          }),
        },
      },
    })
    incidentRootCauseDescription: Omit<IncidentRootCauseDescription, 'id'>,
  ): Promise<IncidentRootCauseDescription> {
    return this.incidentRootCauseDescriptionRepository.create(incidentRootCauseDescription);
  }

  @get('/incident-root-cause-descriptions/count')
  @response(200, {
    description: 'IncidentRootCauseDescription model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(IncidentRootCauseDescription) where?: Where<IncidentRootCauseDescription>,
  ): Promise<Count> {
    return this.incidentRootCauseDescriptionRepository.count(where);
  }

  @get('/incident-root-cause-descriptions')
  @response(200, {
    description: 'Array of IncidentRootCauseDescription model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(IncidentRootCauseDescription, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(IncidentRootCauseDescription) filter?: Filter<IncidentRootCauseDescription>,
  ): Promise<IncidentRootCauseDescription[]> {
    return this.incidentRootCauseDescriptionRepository.find(filter);
  }

  @patch('/incident-root-cause-descriptions')
  @response(200, {
    description: 'IncidentRootCauseDescription PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(IncidentRootCauseDescription, {partial: true}),
        },
      },
    })
    incidentRootCauseDescription: IncidentRootCauseDescription,
    @param.where(IncidentRootCauseDescription) where?: Where<IncidentRootCauseDescription>,
  ): Promise<Count> {
    return this.incidentRootCauseDescriptionRepository.updateAll(incidentRootCauseDescription, where);
  }

  @get('/incident-root-cause-descriptions/{id}')
  @response(200, {
    description: 'IncidentRootCauseDescription model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(IncidentRootCauseDescription, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(IncidentRootCauseDescription, {exclude: 'where'}) filter?: FilterExcludingWhere<IncidentRootCauseDescription>
  ): Promise<IncidentRootCauseDescription> {
    return this.incidentRootCauseDescriptionRepository.findById(id, filter);
  }

  @patch('/incident-root-cause-descriptions/{id}')
  @response(204, {
    description: 'IncidentRootCauseDescription PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(IncidentRootCauseDescription, {partial: true}),
        },
      },
    })
    incidentRootCauseDescription: IncidentRootCauseDescription,
  ): Promise<void> {
    await this.incidentRootCauseDescriptionRepository.updateById(id, incidentRootCauseDescription);
  }

  @put('/incident-root-cause-descriptions/{id}')
  @response(204, {
    description: 'IncidentRootCauseDescription PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() incidentRootCauseDescription: IncidentRootCauseDescription,
  ): Promise<void> {
    await this.incidentRootCauseDescriptionRepository.replaceById(id, incidentRootCauseDescription);
  }

  @del('/incident-root-cause-descriptions/{id}')
  @response(204, {
    description: 'IncidentRootCauseDescription DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.incidentRootCauseDescriptionRepository.deleteById(id);
  }
}
