import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {AuditGmsThree, AuditGmsThreeRelations} from '../models';

export class AuditGmsThreeRepository extends DefaultCrudRepository<
  AuditGmsThree,
  typeof AuditGmsThree.prototype.id,
  AuditGmsThreeRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(AuditGmsThree, dataSource);
  }
}
