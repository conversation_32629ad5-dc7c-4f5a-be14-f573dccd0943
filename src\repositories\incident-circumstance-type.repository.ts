import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {IncidentCircumstanceType, IncidentCircumstanceTypeRelations, IncidentCircumstanceDescription} from '../models';
import {IncidentCircumstanceDescriptionRepository} from './incident-circumstance-description.repository';

export class IncidentCircumstanceTypeRepository extends DefaultCrudRepository<
  IncidentCircumstanceType,
  typeof IncidentCircumstanceType.prototype.id,
  IncidentCircumstanceTypeRelations
> {

  public readonly incidentCircumstanceDescriptions: HasManyRepositoryFactory<IncidentCircumstanceDescription, typeof IncidentCircumstanceType.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('IncidentCircumstanceDescriptionRepository') protected incidentCircumstanceDescriptionRepositoryGetter: Getter<IncidentCircumstanceDescriptionRepository>,
  ) {
    super(IncidentCircumstanceType, dataSource);
    this.incidentCircumstanceDescriptions = this.createHasManyRepositoryFactoryFor('incidentCircumstanceDescriptions', incidentCircumstanceDescriptionRepositoryGetter,);
    this.registerInclusionResolver('incidentCircumstanceDescriptions', this.incidentCircumstanceDescriptions.inclusionResolver);
  }
}
