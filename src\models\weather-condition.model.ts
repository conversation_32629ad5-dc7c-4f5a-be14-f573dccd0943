import {Entity, model, property} from '@loopback/repository';

@model()
export class WeatherCondition extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  description?: string;


  constructor(data?: Partial<WeatherCondition>) {
    super(data);
  }
}

export interface WeatherConditionRelations {
  // describe navigational properties here
}

export type WeatherConditionWithRelations = WeatherCondition & WeatherConditionRelations;
