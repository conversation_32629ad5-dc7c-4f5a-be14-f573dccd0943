import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  AuditFinding,
  Audit,
} from '../models';
import {AuditFindingRepository} from '../repositories';

export class AuditFindingAuditController {
  constructor(
    @repository(AuditFindingRepository)
    public auditFindingRepository: AuditFindingRepository,
  ) { }

  @get('/audit-findings/{id}/audit', {
    responses: {
      '200': {
        description: 'Audit belonging to AuditFinding',
        content: {
          'application/json': {
            schema: getModelSchemaRef(Audit),
          },
        },
      },
    },
  })
  async getAudit(
    @param.path.string('id') id: typeof AuditFinding.prototype.id,
  ): Promise<Audit> {
    return this.auditFindingRepository.audit(id);
  }
}
