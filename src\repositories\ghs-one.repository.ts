import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {GhsOne, GhsOneRelations, GhsTwo} from '../models';
import {GhsTwoRepository} from './ghs-two.repository';

export class GhsOneRepository extends DefaultCrudRepository<
  GhsOne,
  typeof GhsOne.prototype.id,
  GhsOneRelations
> {

  public readonly ghsTwos: HasManyRepositoryFactory<GhsTwo, typeof GhsOne.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('GhsTwoRepository') protected ghsTwoRepositoryGetter: Getter<GhsTwoRepository>,
  ) {
    super(GhsOne, dataSource);
    this.ghsTwos = this.createHasManyRepositoryFactoryFor('ghsTwos', ghsTwoRepositoryGetter,);
    this.registerInclusionResolver('ghsTwos', this.ghsTwos.inclusionResolver);
  }
}
