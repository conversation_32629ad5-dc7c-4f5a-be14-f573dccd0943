import {Entity, model, property, hasMany} from '@loopback/repository';
import {HazardDescription} from './hazard-description.model';

@model()
export class HazardType extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'string',
  })
  hazardCategoryId?: string;

  @hasMany(() => HazardDescription)
  hazardDescriptions: HazardDescription[];

  constructor(data?: Partial<HazardType>) {
    super(data);
  }
}

export interface HazardTypeRelations {
  // describe navigational properties here
}

export type HazardTypeWithRelations = HazardType & HazardTypeRelations;
