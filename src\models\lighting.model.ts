import {Entity, model, property} from '@loopback/repository';

@model()
export class Lighting extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  description?: string;


  constructor(data?: Partial<Lighting>) {
    super(data);
  }
}

export interface LightingRelations {
  // describe navigational properties here
}

export type LightingWithRelations = Lighting & LightingRelations;
