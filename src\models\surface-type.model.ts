import {Entity, model, property} from '@loopback/repository';

@model()
export class SurfaceType extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  description?: string;


  constructor(data?: Partial<SurfaceType>) {
    super(data);
  }
}

export interface SurfaceTypeRelations {
  // describe navigational properties here
}

export type SurfaceTypeWithRelations = SurfaceType & SurfaceTypeRelations;
