import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  IncidentCircumstanceType,
  IncidentCircumstanceDescription,
} from '../models';
import {IncidentCircumstanceTypeRepository} from '../repositories';

export class IncidentCircumstanceTypeIncidentCircumstanceDescriptionController {
  constructor(
    @repository(IncidentCircumstanceTypeRepository) protected incidentCircumstanceTypeRepository: IncidentCircumstanceTypeRepository,
  ) { }

  @get('/incident-circumstance-types/{id}/incident-circumstance-descriptions', {
    responses: {
      '200': {
        description: 'Array of IncidentCircumstanceType has many IncidentCircumstanceDescription',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(IncidentCircumstanceDescription)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<IncidentCircumstanceDescription>,
  ): Promise<IncidentCircumstanceDescription[]> {
    return this.incidentCircumstanceTypeRepository.incidentCircumstanceDescriptions(id).find(filter);
  }

  @post('/incident-circumstance-types/{id}/incident-circumstance-descriptions', {
    responses: {
      '200': {
        description: 'IncidentCircumstanceType model instance',
        content: {'application/json': {schema: getModelSchemaRef(IncidentCircumstanceDescription)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof IncidentCircumstanceType.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(IncidentCircumstanceDescription, {
            title: 'NewIncidentCircumstanceDescriptionInIncidentCircumstanceType',
            exclude: ['id'],
            optional: ['incidentCircumstanceTypeId']
          }),
        },
      },
    }) incidentCircumstanceDescription: Omit<IncidentCircumstanceDescription, 'id'>,
  ): Promise<IncidentCircumstanceDescription> {
    return this.incidentCircumstanceTypeRepository.incidentCircumstanceDescriptions(id).create(incidentCircumstanceDescription);
  }

  @patch('/incident-circumstance-types/{id}/incident-circumstance-descriptions', {
    responses: {
      '200': {
        description: 'IncidentCircumstanceType.IncidentCircumstanceDescription PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(IncidentCircumstanceDescription, {partial: true}),
        },
      },
    })
    incidentCircumstanceDescription: Partial<IncidentCircumstanceDescription>,
    @param.query.object('where', getWhereSchemaFor(IncidentCircumstanceDescription)) where?: Where<IncidentCircumstanceDescription>,
  ): Promise<Count> {
    return this.incidentCircumstanceTypeRepository.incidentCircumstanceDescriptions(id).patch(incidentCircumstanceDescription, where);
  }

  @del('/incident-circumstance-types/{id}/incident-circumstance-descriptions', {
    responses: {
      '200': {
        description: 'IncidentCircumstanceType.IncidentCircumstanceDescription DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(IncidentCircumstanceDescription)) where?: Where<IncidentCircumstanceDescription>,
  ): Promise<Count> {
    return this.incidentCircumstanceTypeRepository.incidentCircumstanceDescriptions(id).delete(where);
  }
}
