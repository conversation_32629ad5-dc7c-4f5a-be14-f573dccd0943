import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {OtherRole, OtherRoleRelations} from '../models';

export class OtherRoleRepository extends DefaultCrudRepository<
  OtherRole,
  typeof OtherRole.prototype.id,
  OtherRoleRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(OtherRole, dataSource);
  }
}
