import {Entity, model, property, hasMany} from '@loopback/repository';
import {LocationFive} from './location-five.model';
import {UserLocationRole} from './user-location-role.model';

@model()
export class LocationFour extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'string',
  })
  startDate?: string;


  @property({
    type: 'string',
  })
  endDate?: string;

  @property({
    type: 'number',
  })
  order?: number;
  
  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'string',
  })
  modified?: string;

  @property({
    type: 'string',
  })
  locationThreeId?: string;

  @hasMany(() => LocationFive)
  locationFives: LocationFive[];

  @hasMany(() => UserLocationRole)
  userLocationRoles: UserLocationRole[];

  constructor(data?: Partial<LocationFour>) {
    super(data);
  }
}

export interface LocationFourRelations {
  // describe navigational properties here
}

export type LocationFourWithRelations = LocationFour & LocationFourRelations;
