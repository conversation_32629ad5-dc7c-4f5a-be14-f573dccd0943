import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {HumanTwo, HumanTwoRelations} from '../models';

export class HumanTwoRepository extends DefaultCrudRepository<
  HumanTwo,
  typeof HumanTwo.prototype.id,
  HumanTwoRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(HumanTwo, dataSource);
  }
}
