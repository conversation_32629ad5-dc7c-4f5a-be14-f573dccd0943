import {Entity, model, property} from '@loopback/repository';

@model()
export class RiskCategory extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  description?: string;


  constructor(data?: Partial<RiskCategory>) {
    super(data);
  }
}

export interface RiskCategoryRelations {
  // describe navigational properties here
}

export type RiskCategoryWithRelations = RiskCategory & RiskCategoryRelations;
