import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {SurfaceCondition} from '../models';
import {SurfaceConditionRepository} from '../repositories';

export class SurfaceConditionController {
  constructor(
    @repository(SurfaceConditionRepository)
    public surfaceConditionRepository : SurfaceConditionRepository,
  ) {}

  @post('/surface-conditions')
  @response(200, {
    description: 'SurfaceCondition model instance',
    content: {'application/json': {schema: getModelSchemaRef(SurfaceCondition)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SurfaceCondition, {
            title: 'NewSurfaceCondition',
            exclude: ['id'],
          }),
        },
      },
    })
    surfaceCondition: Omit<SurfaceCondition, 'id'>,
  ): Promise<SurfaceCondition> {
    return this.surfaceConditionRepository.create(surfaceCondition);
  }

  @get('/surface-conditions/count')
  @response(200, {
    description: 'SurfaceCondition model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(SurfaceCondition) where?: Where<SurfaceCondition>,
  ): Promise<Count> {
    return this.surfaceConditionRepository.count(where);
  }

  @get('/surface-conditions')
  @response(200, {
    description: 'Array of SurfaceCondition model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(SurfaceCondition, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(SurfaceCondition) filter?: Filter<SurfaceCondition>,
  ): Promise<SurfaceCondition[]> {
    return this.surfaceConditionRepository.find(filter);
  }

  @patch('/surface-conditions')
  @response(200, {
    description: 'SurfaceCondition PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SurfaceCondition, {partial: true}),
        },
      },
    })
    surfaceCondition: SurfaceCondition,
    @param.where(SurfaceCondition) where?: Where<SurfaceCondition>,
  ): Promise<Count> {
    return this.surfaceConditionRepository.updateAll(surfaceCondition, where);
  }

  @get('/surface-conditions/{id}')
  @response(200, {
    description: 'SurfaceCondition model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(SurfaceCondition, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(SurfaceCondition, {exclude: 'where'}) filter?: FilterExcludingWhere<SurfaceCondition>
  ): Promise<SurfaceCondition> {
    return this.surfaceConditionRepository.findById(id, filter);
  }

  @patch('/surface-conditions/{id}')
  @response(204, {
    description: 'SurfaceCondition PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SurfaceCondition, {partial: true}),
        },
      },
    })
    surfaceCondition: SurfaceCondition,
  ): Promise<void> {
    await this.surfaceConditionRepository.updateById(id, surfaceCondition);
  }

  @put('/surface-conditions/{id}')
  @response(204, {
    description: 'SurfaceCondition PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() surfaceCondition: SurfaceCondition,
  ): Promise<void> {
    await this.surfaceConditionRepository.replaceById(id, surfaceCondition);
  }

  @del('/surface-conditions/{id}')
  @response(204, {
    description: 'SurfaceCondition DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.surfaceConditionRepository.deleteById(id);
  }
}
