import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {Inspection} from '../models';
import {InspectionRepository, ActionRepository} from '../repositories';

export class InspectionController {
  constructor(
    @repository(InspectionRepository)
    public inspectionRepository : InspectionRepository,
    @repository(ActionRepository)
    public actionRepository : ActionRepository,
  ) {}

  @post('/inspections')
  @response(200, {
    description: 'Inspection model instance',
    content: {'application/json': {schema: getModelSchemaRef(Inspection)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Inspection, {
            title: 'NewInspection',
            exclude: ['id'],
          }),
        },
      },
    })
    inspection: Omit<Inspection, 'id'>,
  ): Promise<Inspection> {
    return this.inspectionRepository.create(inspection);
  }

  @get('/inspections/count')
  @response(200, {
    description: 'Inspection model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(Inspection) where?: Where<Inspection>,
  ): Promise<Count> {
    return this.inspectionRepository.count(where);
  }

  @get('/inspections')
  @response(200, {
    description: 'Array of Inspection model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Inspection, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(Inspection) filter?: Filter<Inspection>,
  ): Promise<Inspection[]> {
    return this.inspectionRepository.find(filter);
  }

  @patch('/inspections')
  @response(200, {
    description: 'Inspection PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Inspection, {partial: true}),
        },
      },
    })
    inspection: Inspection,
    @param.where(Inspection) where?: Where<Inspection>,
  ): Promise<Count> {
    return this.inspectionRepository.updateAll(inspection, where);
  }

  @get('/inspections/{id}')
  @response(200, {
    description: 'Inspection model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(Inspection, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(Inspection, {exclude: 'where'}) filter?: FilterExcludingWhere<Inspection>
  ): Promise<Inspection> {
    return this.inspectionRepository.findById(id, filter);
  }

  @patch('/inspections/{id}')
  @response(204, {
    description: 'Inspection PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Inspection, {partial: true}),
        },
      },
    })
    inspection: Inspection,
  ): Promise<void> {
    await this.inspectionRepository.updateById(id, inspection);
  }

  @put('/inspections/{id}')
  @response(204, {
    description: 'Inspection PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() inspection: Inspection,
  ): Promise<void> {
    await this.inspectionRepository.replaceById(id, inspection);
  }

  @del('/inspections/{id}')
  @response(204, {
    description: 'Inspection DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.inspectionRepository.deleteById(id);
    await this.actionRepository.deleteAll({objectId: id})
  }
}
