import { Entity, model, property } from '@loopback/repository';

@model()
export class HumanTwo extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;
  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'number',
  })
  order?: number;

  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'string',
  })
  modified?: string;
  @property({
    type: 'string',
  })
  humanOneId?: string;

  constructor(data?: Partial<HumanTwo>) {
    super(data);
  }
}

export interface HumanTwoRelations {
  // describe navigational properties here
}

export type HumanTwoWithRelations = HumanTwo & HumanTwoRelations;
