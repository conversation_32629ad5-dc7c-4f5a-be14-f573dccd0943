import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {HazardGms} from '../models';
import {HazardGmsRepository} from '../repositories';

export class HazardGmsController {
  constructor(
    @repository(HazardGmsRepository)
    public hazardGmsRepository : HazardGmsRepository,
  ) {}

  @post('/hazard-gms')
  @response(200, {
    description: 'HazardGms model instance',
    content: {'application/json': {schema: getModelSchemaRef(HazardGms)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(HazardGms, {
            title: 'NewHazardGms',
            exclude: ['id'],
          }),
        },
      },
    })
    hazardGms: Omit<HazardGms, 'id'>,
  ): Promise<HazardGms> {
    return this.hazardGmsRepository.create(hazardGms);
  }

  @get('/hazard-gms/count')
  @response(200, {
    description: 'HazardGms model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(HazardGms) where?: Where<HazardGms>,
  ): Promise<Count> {
    return this.hazardGmsRepository.count(where);
  }

  @get('/hazard-gms')
  @response(200, {
    description: 'Array of HazardGms model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(HazardGms, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(HazardGms) filter?: Filter<HazardGms>,
  ): Promise<HazardGms[]> {
    return this.hazardGmsRepository.find(filter);
  }

  @patch('/hazard-gms')
  @response(200, {
    description: 'HazardGms PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(HazardGms, {partial: true}),
        },
      },
    })
    hazardGms: HazardGms,
    @param.where(HazardGms) where?: Where<HazardGms>,
  ): Promise<Count> {
    return this.hazardGmsRepository.updateAll(hazardGms, where);
  }

  @get('/hazard-gms/{id}')
  @response(200, {
    description: 'HazardGms model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(HazardGms, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(HazardGms, {exclude: 'where'}) filter?: FilterExcludingWhere<HazardGms>
  ): Promise<HazardGms> {
    return this.hazardGmsRepository.findById(id, filter);
  }

  @patch('/hazard-gms/{id}')
  @response(204, {
    description: 'HazardGms PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(HazardGms, {partial: true}),
        },
      },
    })
    hazardGms: HazardGms,
  ): Promise<void> {
    await this.hazardGmsRepository.updateById(id, hazardGms);
  }

  @put('/hazard-gms/{id}')
  @response(204, {
    description: 'HazardGms PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() hazardGms: HazardGms,
  ): Promise<void> {
    await this.hazardGmsRepository.replaceById(id, hazardGms);
  }

  @del('/hazard-gms/{id}')
  @response(204, {
    description: 'HazardGms DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.hazardGmsRepository.deleteById(id);
  }
}
