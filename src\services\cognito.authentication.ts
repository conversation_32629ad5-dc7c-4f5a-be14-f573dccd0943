import { inject, injectable } from '@loopback/core';
import {
    AuthenticationBindings,
    AuthenticationMetadata,
    AuthenticationStrategy,
} from '@loopback/authentication';
import { HttpErrors, Request } from '@loopback/rest';
import { CognitoJwtVerifier } from "aws-jwt-verify";
import { SecurityBindings, securityId, UserProfile } from '@loopback/security';

@injectable()
export class AWSJWTAuthenticationStrategy implements AuthenticationStrategy {
    name: string = 'aws-jwt';

    constructor(
        @inject(AuthenticationBindings.METADATA)
        private metadata: AuthenticationMetadata,
    ) { }

    async authenticate(request: Request): Promise<UserProfile | undefined> {
        const token = this.extractCredentials(request);
        if (!token) {
            throw new HttpErrors.Unauthorized('JWT token is missing');
        }

        try {
            const verifier = CognitoJwtVerifier.create({
                userPoolId: `${process.env.AWS_USER_POOL_ID}`,
                tokenUse: "access",
                clientId: `${process.env.AWS_CLIENT_ID}`
            });

            try {
                const payload = await verifier.verify(
                    token
                );

                return {
                    [securityId]: payload.sub,
                    name: payload.username
                }
                console.log("Token is valid. Payload:", payload);
            } catch {
                console.log("Token not valid!");
            }
            // Customize this part to map the user profile
            
        } catch (error) {
            throw new HttpErrors.Unauthorized(`JWT token invalid: ${error.message}`);
        }
    }

    extractCredentials(request: Request): string | undefined {
        // Customize this part to extract the JWT token from the request
        const authHeader = request.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return undefined;
        }
        return authHeader.substring(7);
    }
}
