import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {IncidentRootCauseDescription, IncidentRootCauseDescriptionRelations} from '../models';

export class IncidentRootCauseDescriptionRepository extends DefaultCrudRepository<
  IncidentRootCauseDescription,
  typeof IncidentRootCauseDescription.prototype.id,
  IncidentRootCauseDescriptionRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(IncidentRootCauseDescription, dataSource);
  }
}
