import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {Lighting, LightingRelations} from '../models';

export class LightingRepository extends DefaultCrudRepository<
  Lighting,
  typeof Lighting.prototype.id,
  LightingRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(Lighting, dataSource);
  }
}
