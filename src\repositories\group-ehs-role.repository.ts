import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {GroupEhsRole, GroupEhsRoleRelations} from '../models';

export class GroupEhsRoleRepository extends DefaultCrudRepository<
  GroupEhsRole,
  typeof GroupEhsRole.prototype.id,
  GroupEhsRoleRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(GroupEhsRole, dataSource);
  }
}
