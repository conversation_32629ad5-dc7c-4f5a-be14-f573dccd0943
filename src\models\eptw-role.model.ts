import {Entity, model, property} from '@loopback/repository';

@model()
export class EptwRole extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'number',
  })
  order?: number;
  
  @property({
    type: 'any',
  })
  permission?: any;

  @property({
    type: 'date',
  })
  created?: string;


  constructor(data?: Partial<EptwRole>) {
    super(data);
  }
}

export interface EptwRoleRelations {
  // describe navigational properties here
}

export type EptwRoleWithRelations = EptwRole & EptwRoleRelations;
