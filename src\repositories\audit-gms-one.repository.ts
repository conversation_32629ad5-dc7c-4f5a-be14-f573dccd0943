import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {AuditGmsOne, AuditGmsOneRelations, AuditGmsTwo} from '../models';
import {AuditGmsTwoRepository} from './audit-gms-two.repository';

export class AuditGmsOneRepository extends DefaultCrudRepository<
  AuditGmsOne,
  typeof AuditGmsOne.prototype.id,
  AuditGmsOneRelations
> {

  public readonly auditGmsTwos: HasManyRepositoryFactory<AuditGmsTwo, typeof AuditGmsOne.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('AuditGmsTwoRepository') protected auditGmsTwoRepositoryGetter: Getter<AuditGmsTwoRepository>,
  ) {
    super(AuditGmsOne, dataSource);
    this.auditGmsTwos = this.createHasManyRepositoryFactoryFor('auditGmsTwos', auditGmsTwoRepositoryGetter,);
    this.registerInclusionResolver('auditGmsTwos', this.auditGmsTwos.inclusionResolver);
  }
}
