import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {IncidentUnderlyingCauseType, IncidentUnderlyingCauseTypeRelations, IncidentUnderlyingCauseDescription} from '../models';
import {IncidentUnderlyingCauseDescriptionRepository} from './incident-underlying-cause-description.repository';

export class IncidentUnderlyingCauseTypeRepository extends DefaultCrudRepository<
  IncidentUnderlyingCauseType,
  typeof IncidentUnderlyingCauseType.prototype.id,
  IncidentUnderlyingCauseTypeRelations
> {

  public readonly incidentUnderlyingCauseDescriptions: HasManyRepositoryFactory<IncidentUnderlyingCauseDescription, typeof IncidentUnderlyingCauseType.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('IncidentUnderlyingCauseDescriptionRepository') protected incidentUnderlyingCauseDescriptionRepositoryGetter: Getter<IncidentUnderlyingCauseDescriptionRepository>,
  ) {
    super(IncidentUnderlyingCauseType, dataSource);
    this.incidentUnderlyingCauseDescriptions = this.createHasManyRepositoryFactoryFor('incidentUnderlyingCauseDescriptions', incidentUnderlyingCauseDescriptionRepositoryGetter,);
    this.registerInclusionResolver('incidentUnderlyingCauseDescriptions', this.incidentUnderlyingCauseDescriptions.inclusionResolver);
  }
}
