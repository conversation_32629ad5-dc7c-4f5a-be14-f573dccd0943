import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  AuditFinding,
  User,
} from '../models';
import {AuditFindingRepository} from '../repositories';

export class AuditFindingUserController {
  constructor(
    @repository(AuditFindingRepository)
    public auditFindingRepository: AuditFindingRepository,
  ) { }

  @get('/audit-findings/{id}/user', {
    responses: {
      '200': {
        description: 'User belonging to AuditFinding',
        content: {
          'application/json': {
            schema: getModelSchemaRef(User),
          },
        },
      },
    },
  })
  async getUser(
    @param.path.string('id') id: typeof AuditFinding.prototype.id,
  ): Promise<User> {
    return this.auditFindingRepository.assignedTo(id);
  }
}
