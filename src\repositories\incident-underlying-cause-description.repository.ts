import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {IncidentUnderlyingCauseDescription, IncidentUnderlyingCauseDescriptionRelations} from '../models';

export class IncidentUnderlyingCauseDescriptionRepository extends DefaultCrudRepository<
  IncidentUnderlyingCauseDescription,
  typeof IncidentUnderlyingCauseDescription.prototype.id,
  IncidentUnderlyingCauseDescriptionRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(IncidentUnderlyingCauseDescription, dataSource);
  }
}
