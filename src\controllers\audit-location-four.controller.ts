import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  Audit,
  LocationFour,
} from '../models';
import {AuditRepository} from '../repositories';

export class AuditLocationFourController {
  constructor(
    @repository(AuditRepository)
    public auditRepository: AuditRepository,
  ) { }

  @get('/audits/{id}/location-four', {
    responses: {
      '200': {
        description: 'LocationFour belonging to Audit',
        content: {
          'application/json': {
            schema: getModelSchemaRef(LocationFour),
          },
        },
      },
    },
  })
  async getLocationFour(
    @param.path.string('id') id: typeof Audit.prototype.id,
  ): Promise<LocationFour> {
    return this.auditRepository.locationFour(id);
  }
}
