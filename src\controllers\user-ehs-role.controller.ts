import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  User,
  EhsRole,
} from '../models';
import {UserRepository} from '../repositories';
import { authenticate } from '@loopback/authentication';
@authenticate('jwt')
export class UserEhsRoleController {
  constructor(
    @repository(UserRepository)
    public userRepository: UserRepository,
  ) { }

  @get('/users/{id}/ehs-role', {
    responses: {
      '200': {
        description: 'EhsRole belonging to User',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(EhsRole)},
          },
        },
      },
    },
  })
  async getEhsRole(
    @param.path.string('id') id: typeof User.prototype.id,
  ): Promise<EhsRole> {
    return this.userRepository.ehsRole(id);
  }
}
