import {Entity, model, property, hasMany} from '@loopback/repository';
import {IncidentCircumstanceType} from './incident-circumstance-type.model';

@model()
export class IncidentCircumstanceCategory extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'string',
  })
  description?: string;

  @hasMany(() => IncidentCircumstanceType)
  incidentCircumstanceTypes: IncidentCircumstanceType[];

  constructor(data?: Partial<IncidentCircumstanceCategory>) {
    super(data);
  }
}

export interface IncidentCircumstanceCategoryRelations {
  // describe navigational properties here
}

export type IncidentCircumstanceCategoryWithRelations = IncidentCircumstanceCategory & IncidentCircumstanceCategoryRelations;
