import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ReportData,
  LocationTwo,
} from '../models';
import {ReportDataRepository} from '../repositories';

export class ReportDataLocationTwoController {
  constructor(
    @repository(ReportDataRepository)
    public reportDataRepository: ReportDataRepository,
  ) { }

  @get('/report-data/{id}/location-two', {
    responses: {
      '200': {
        description: 'LocationTwo belonging to ReportData',
        content: {
          'application/json': {
            schema: getModelSchemaRef(LocationTwo),
          },
        },
      },
    },
  })
  async getLocationTwo(
    @param.path.string('id') id: typeof ReportData.prototype.id,
  ): Promise<LocationTwo> {
    return this.reportDataRepository.locationTwo(id);
  }
}
