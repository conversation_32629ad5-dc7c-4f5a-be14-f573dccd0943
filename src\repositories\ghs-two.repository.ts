import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {GhsTwo, GhsTwoRelations, GhsThree} from '../models';
import {GhsThreeRepository} from './ghs-three.repository';

export class GhsTwoRepository extends DefaultCrudRepository<
  GhsTwo,
  typeof GhsTwo.prototype.id,
  GhsTwoRelations
> {

  public readonly ghsThrees: HasManyRepositoryFactory<GhsThree, typeof GhsTwo.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('GhsThreeRepository') protected ghsThreeRepositoryGetter: Getter<GhsThreeRepository>,
  ) {
    super(GhsTwo, dataSource);
    this.ghsThrees = this.createHasManyRepositoryFactoryFor('ghsThrees', ghsThreeRepositoryGetter,);
    this.registerInclusionResolver('ghsThrees', this.ghsThrees.inclusionResolver);
  }
}
