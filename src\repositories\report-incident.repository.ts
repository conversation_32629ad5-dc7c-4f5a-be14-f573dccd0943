import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {ReportIncident, ReportIncidentRelations, LocationTwo, LocationThree, LocationSix, LocationOne, LocationFour, LocationFive, Lighting, SurfaceType, SurfaceCondition, RiskCategory, IncidentUnderlyingCause, IncidentUnderlyingCauseType, IncidentUnderlyingCauseDescription, IncidentRootCauseType, IncidentRootCauseDescription, WeatherCondition, WorkActivity, IncidentCircumstanceCategory, IncidentCircumstanceDescription, IncidentCircumstanceType, User} from '../models';
import {LocationTwoRepository} from './location-two.repository';
import {LocationThreeRepository} from './location-three.repository';
import {LocationSixRepository} from './location-six.repository';
import {LocationOneRepository} from './location-one.repository';
import {LocationFourRepository} from './location-four.repository';
import {LocationFiveRepository} from './location-five.repository';
import {LightingRepository} from './lighting.repository';
import {SurfaceTypeRepository} from './surface-type.repository';
import {SurfaceConditionRepository} from './surface-condition.repository';
import {RiskCategoryRepository} from './risk-category.repository';
import {IncidentUnderlyingCauseRepository} from './incident-underlying-cause.repository';
import {IncidentUnderlyingCauseTypeRepository} from './incident-underlying-cause-type.repository';
import {IncidentUnderlyingCauseDescriptionRepository} from './incident-underlying-cause-description.repository';
import {IncidentRootCauseTypeRepository} from './incident-root-cause-type.repository';
import {IncidentRootCauseDescriptionRepository} from './incident-root-cause-description.repository';
import {WeatherConditionRepository} from './weather-condition.repository';
import {WorkActivityRepository} from './work-activity.repository';
import {IncidentCircumstanceCategoryRepository} from './incident-circumstance-category.repository';
import {IncidentCircumstanceDescriptionRepository} from './incident-circumstance-description.repository';
import {IncidentCircumstanceTypeRepository} from './incident-circumstance-type.repository';
import {UserRepository} from './user.repository';

export class ReportIncidentRepository extends DefaultCrudRepository<
  ReportIncident,
  typeof ReportIncident.prototype.id,
  ReportIncidentRelations
> {

  public readonly locationTwo: BelongsToAccessor<LocationTwo, typeof ReportIncident.prototype.id>;

  public readonly locationThree: BelongsToAccessor<LocationThree, typeof ReportIncident.prototype.id>;

  public readonly locationSix: BelongsToAccessor<LocationSix, typeof ReportIncident.prototype.id>;

  public readonly locationOne: BelongsToAccessor<LocationOne, typeof ReportIncident.prototype.id>;

  public readonly locationFour: BelongsToAccessor<LocationFour, typeof ReportIncident.prototype.id>;

  public readonly locationFive: BelongsToAccessor<LocationFive, typeof ReportIncident.prototype.id>;

  public readonly lighting: BelongsToAccessor<Lighting, typeof ReportIncident.prototype.id>;

  public readonly surfaceType: BelongsToAccessor<SurfaceType, typeof ReportIncident.prototype.id>;

  public readonly surfaceCondition: BelongsToAccessor<SurfaceCondition, typeof ReportIncident.prototype.id>;

  public readonly riskCategory: BelongsToAccessor<RiskCategory, typeof ReportIncident.prototype.id>;

  public readonly incidentUnderlyingCause: BelongsToAccessor<IncidentUnderlyingCause, typeof ReportIncident.prototype.id>;

  public readonly incidentUnderlyingCauseType: BelongsToAccessor<IncidentUnderlyingCauseType, typeof ReportIncident.prototype.id>;

  public readonly incidentUnderlyingCauseDescription: BelongsToAccessor<IncidentUnderlyingCauseDescription, typeof ReportIncident.prototype.id>;

  public readonly incidentRootCauseType: BelongsToAccessor<IncidentRootCauseType, typeof ReportIncident.prototype.id>;

  public readonly incidentRootCauseDescription: BelongsToAccessor<IncidentRootCauseDescription, typeof ReportIncident.prototype.id>;

  public readonly weatherCondition: BelongsToAccessor<WeatherCondition, typeof ReportIncident.prototype.id>;

  public readonly workActivity: BelongsToAccessor<WorkActivity, typeof ReportIncident.prototype.id>;

  public readonly incidentCircumstanceCategory: BelongsToAccessor<IncidentCircumstanceCategory, typeof ReportIncident.prototype.id>;

  public readonly incidentCircumstanceDescription: BelongsToAccessor<IncidentCircumstanceDescription, typeof ReportIncident.prototype.id>;

  public readonly incidentCircumstanceType: BelongsToAccessor<IncidentCircumstanceType, typeof ReportIncident.prototype.id>;

  public readonly user: BelongsToAccessor<User, typeof ReportIncident.prototype.id>;

  public readonly reviewer: BelongsToAccessor<User, typeof ReportIncident.prototype.id>;

  public readonly investigator: BelongsToAccessor<User, typeof ReportIncident.prototype.id>;

  public readonly incidentOwner: BelongsToAccessor<User, typeof ReportIncident.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('LocationTwoRepository') protected locationTwoRepositoryGetter: Getter<LocationTwoRepository>, @repository.getter('LocationThreeRepository') protected locationThreeRepositoryGetter: Getter<LocationThreeRepository>, @repository.getter('LocationSixRepository') protected locationSixRepositoryGetter: Getter<LocationSixRepository>, @repository.getter('LocationOneRepository') protected locationOneRepositoryGetter: Getter<LocationOneRepository>, @repository.getter('LocationFourRepository') protected locationFourRepositoryGetter: Getter<LocationFourRepository>, @repository.getter('LocationFiveRepository') protected locationFiveRepositoryGetter: Getter<LocationFiveRepository>, @repository.getter('LightingRepository') protected lightingRepositoryGetter: Getter<LightingRepository>, @repository.getter('SurfaceTypeRepository') protected surfaceTypeRepositoryGetter: Getter<SurfaceTypeRepository>, @repository.getter('SurfaceConditionRepository') protected surfaceConditionRepositoryGetter: Getter<SurfaceConditionRepository>, @repository.getter('RiskCategoryRepository') protected riskCategoryRepositoryGetter: Getter<RiskCategoryRepository>, @repository.getter('IncidentUnderlyingCauseRepository') protected incidentUnderlyingCauseRepositoryGetter: Getter<IncidentUnderlyingCauseRepository>, @repository.getter('IncidentUnderlyingCauseTypeRepository') protected incidentUnderlyingCauseTypeRepositoryGetter: Getter<IncidentUnderlyingCauseTypeRepository>, @repository.getter('IncidentUnderlyingCauseDescriptionRepository') protected incidentUnderlyingCauseDescriptionRepositoryGetter: Getter<IncidentUnderlyingCauseDescriptionRepository>, @repository.getter('IncidentRootCauseTypeRepository') protected incidentRootCauseTypeRepositoryGetter: Getter<IncidentRootCauseTypeRepository>, @repository.getter('IncidentRootCauseDescriptionRepository') protected incidentRootCauseDescriptionRepositoryGetter: Getter<IncidentRootCauseDescriptionRepository>, @repository.getter('WeatherConditionRepository') protected weatherConditionRepositoryGetter: Getter<WeatherConditionRepository>, @repository.getter('WorkActivityRepository') protected workActivityRepositoryGetter: Getter<WorkActivityRepository>, @repository.getter('IncidentCircumstanceCategoryRepository') protected incidentCircumstanceCategoryRepositoryGetter: Getter<IncidentCircumstanceCategoryRepository>, @repository.getter('IncidentCircumstanceDescriptionRepository') protected incidentCircumstanceDescriptionRepositoryGetter: Getter<IncidentCircumstanceDescriptionRepository>, @repository.getter('IncidentCircumstanceTypeRepository') protected incidentCircumstanceTypeRepositoryGetter: Getter<IncidentCircumstanceTypeRepository>, @repository.getter('UserRepository') protected userRepositoryGetter: Getter<UserRepository>,
  ) {
    super(ReportIncident, dataSource);
    this.incidentOwner = this.createBelongsToAccessorFor('incidentOwner', userRepositoryGetter,);
    this.registerInclusionResolver('incidentOwner', this.incidentOwner.inclusionResolver);
    this.investigator = this.createBelongsToAccessorFor('investigator', userRepositoryGetter,);
    this.registerInclusionResolver('investigator', this.investigator.inclusionResolver);
    this.reviewer = this.createBelongsToAccessorFor('reviewer', userRepositoryGetter,);
    this.registerInclusionResolver('reviewer', this.reviewer.inclusionResolver);
    this.user = this.createBelongsToAccessorFor('user', userRepositoryGetter,);
    this.registerInclusionResolver('user', this.user.inclusionResolver);
    this.incidentCircumstanceType = this.createBelongsToAccessorFor('incidentCircumstanceType', incidentCircumstanceTypeRepositoryGetter,);
    this.registerInclusionResolver('incidentCircumstanceType', this.incidentCircumstanceType.inclusionResolver);
    this.incidentCircumstanceDescription = this.createBelongsToAccessorFor('incidentCircumstanceDescription', incidentCircumstanceDescriptionRepositoryGetter,);
    this.registerInclusionResolver('incidentCircumstanceDescription', this.incidentCircumstanceDescription.inclusionResolver);
    this.incidentCircumstanceCategory = this.createBelongsToAccessorFor('incidentCircumstanceCategory', incidentCircumstanceCategoryRepositoryGetter,);
    this.registerInclusionResolver('incidentCircumstanceCategory', this.incidentCircumstanceCategory.inclusionResolver);
    this.workActivity = this.createBelongsToAccessorFor('workActivity', workActivityRepositoryGetter,);
    this.registerInclusionResolver('workActivity', this.workActivity.inclusionResolver);
    this.weatherCondition = this.createBelongsToAccessorFor('weatherCondition', weatherConditionRepositoryGetter,);
    this.registerInclusionResolver('weatherCondition', this.weatherCondition.inclusionResolver);
    this.incidentRootCauseDescription = this.createBelongsToAccessorFor('incidentRootCauseDescription', incidentRootCauseDescriptionRepositoryGetter,);
    this.registerInclusionResolver('incidentRootCauseDescription', this.incidentRootCauseDescription.inclusionResolver);
    this.incidentRootCauseType = this.createBelongsToAccessorFor('incidentRootCauseType', incidentRootCauseTypeRepositoryGetter,);
    this.registerInclusionResolver('incidentRootCauseType', this.incidentRootCauseType.inclusionResolver);
    this.incidentUnderlyingCauseDescription = this.createBelongsToAccessorFor('incidentUnderlyingCauseDescription', incidentUnderlyingCauseDescriptionRepositoryGetter,);
    this.registerInclusionResolver('incidentUnderlyingCauseDescription', this.incidentUnderlyingCauseDescription.inclusionResolver);
    this.incidentUnderlyingCauseType = this.createBelongsToAccessorFor('incidentUnderlyingCauseType', incidentUnderlyingCauseTypeRepositoryGetter,);
    this.registerInclusionResolver('incidentUnderlyingCauseType', this.incidentUnderlyingCauseType.inclusionResolver);
    this.incidentUnderlyingCause = this.createBelongsToAccessorFor('incidentUnderlyingCause', incidentUnderlyingCauseRepositoryGetter,);
    this.registerInclusionResolver('incidentUnderlyingCause', this.incidentUnderlyingCause.inclusionResolver);
    this.riskCategory = this.createBelongsToAccessorFor('riskCategory', riskCategoryRepositoryGetter,);
    this.registerInclusionResolver('riskCategory', this.riskCategory.inclusionResolver);
    this.surfaceCondition = this.createBelongsToAccessorFor('surfaceCondition', surfaceConditionRepositoryGetter,);
    this.registerInclusionResolver('surfaceCondition', this.surfaceCondition.inclusionResolver);
    this.surfaceType = this.createBelongsToAccessorFor('surfaceType', surfaceTypeRepositoryGetter,);
    this.registerInclusionResolver('surfaceType', this.surfaceType.inclusionResolver);
    this.lighting = this.createBelongsToAccessorFor('lighting', lightingRepositoryGetter,);
    this.registerInclusionResolver('lighting', this.lighting.inclusionResolver);
    this.locationFive = this.createBelongsToAccessorFor('locationFive', locationFiveRepositoryGetter,);
    this.registerInclusionResolver('locationFive', this.locationFive.inclusionResolver);
    this.locationFour = this.createBelongsToAccessorFor('locationFour', locationFourRepositoryGetter,);
    this.registerInclusionResolver('locationFour', this.locationFour.inclusionResolver);
    this.locationOne = this.createBelongsToAccessorFor('locationOne', locationOneRepositoryGetter,);
    this.registerInclusionResolver('locationOne', this.locationOne.inclusionResolver);
    this.locationSix = this.createBelongsToAccessorFor('locationSix', locationSixRepositoryGetter,);
    this.registerInclusionResolver('locationSix', this.locationSix.inclusionResolver);
    this.locationThree = this.createBelongsToAccessorFor('locationThree', locationThreeRepositoryGetter,);
    this.registerInclusionResolver('locationThree', this.locationThree.inclusionResolver);
    this.locationTwo = this.createBelongsToAccessorFor('locationTwo', locationTwoRepositoryGetter,);
    this.registerInclusionResolver('locationTwo', this.locationTwo.inclusionResolver);
  }
}
