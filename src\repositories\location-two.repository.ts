import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {LocationTwo, LocationTwoRelations, LocationThree, UserLocationRole} from '../models';
import {LocationThreeRepository} from './location-three.repository';
import {UserLocationRoleRepository} from './user-location-role.repository';

export class LocationTwoRepository extends DefaultCrudRepository<
  LocationTwo,
  typeof LocationTwo.prototype.id,
  LocationTwoRelations
> {

  public readonly locationThrees: HasManyRepositoryFactory<LocationThree, typeof LocationTwo.prototype.id>;

  public readonly userLocationRoles: HasManyRepositoryFactory<UserLocationRole, typeof LocationTwo.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('LocationThreeRepository') protected locationThreeRepositoryGetter: Getter<LocationThreeRepository>, @repository.getter('UserLocationRoleRepository') protected userLocationRoleRepositoryGetter: Getter<UserLocationRoleRepository>,
  ) {
    super(LocationTwo, dataSource);
    this.userLocationRoles = this.createHasManyRepositoryFactoryFor('userLocationRoles', userLocationRoleRepositoryGetter,);
    this.registerInclusionResolver('userLocationRoles', this.userLocationRoles.inclusionResolver);
    this.locationThrees = this.createHasManyRepositoryFactoryFor('locationThrees', locationThreeRepositoryGetter,);
    this.registerInclusionResolver('locationThrees', this.locationThrees.inclusionResolver);
  }
}
