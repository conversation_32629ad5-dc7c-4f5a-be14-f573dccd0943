import {Entity, model, property} from '@loopback/repository';

@model()
export class IncidentUnderlyingCauseDescription extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'string',
  })
  incidentUnderlyingCauseTypeId?: string;

  constructor(data?: Partial<IncidentUnderlyingCauseDescription>) {
    super(data);
  }
}

export interface IncidentUnderlyingCauseDescriptionRelations {
  // describe navigational properties here
}

export type IncidentUnderlyingCauseDescriptionWithRelations = IncidentUnderlyingCauseDescription & IncidentUnderlyingCauseDescriptionRelations;
