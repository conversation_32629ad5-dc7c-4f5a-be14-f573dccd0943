import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  LocationFour,
  UserLocationRole,
} from '../models';
import {LocationFourRepository} from '../repositories';

export class LocationFourUserLocationRoleController {
  constructor(
    @repository(LocationFourRepository) protected locationFourRepository: LocationFourRepository,
  ) { }

  @get('/location-fours/{id}/user-location-roles', {
    responses: {
      '200': {
        description: 'Array of LocationFour has many UserLocationRole',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(UserLocationRole)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<UserLocationRole>,
  ): Promise<UserLocationRole[]> {
    return this.locationFourRepository.userLocationRoles(id).find(filter);
  }

  @post('/location-fours/{id}/user-location-roles', {
    responses: {
      '200': {
        description: 'LocationFour model instance',
        content: {'application/json': {schema: getModelSchemaRef(UserLocationRole)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof LocationFour.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(UserLocationRole, {
            title: 'NewUserLocationRoleInLocationFour',
            exclude: ['id'],
            optional: ['locationFourId']
          }),
        },
      },
    }) userLocationRole: Omit<UserLocationRole, 'id'>,
  ): Promise<UserLocationRole> {
    return this.locationFourRepository.userLocationRoles(id).create(userLocationRole);
  }

  @patch('/location-fours/{id}/user-location-roles', {
    responses: {
      '200': {
        description: 'LocationFour.UserLocationRole PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(UserLocationRole, {partial: true}),
        },
      },
    })
    userLocationRole: Partial<UserLocationRole>,
    @param.query.object('where', getWhereSchemaFor(UserLocationRole)) where?: Where<UserLocationRole>,
  ): Promise<Count> {
    return this.locationFourRepository.userLocationRoles(id).patch(userLocationRole, where);
  }

  @del('/location-fours/{id}/user-location-roles', {
    responses: {
      '200': {
        description: 'LocationFour.UserLocationRole DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(UserLocationRole)) where?: Where<UserLocationRole>,
  ): Promise<Count> {
    return this.locationFourRepository.userLocationRoles(id).delete(where);
  }
}
