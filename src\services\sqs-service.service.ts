import {injectable} from '@loopback/core';
import {SQS, } from 'aws-sdk';
import { User } from '../models';
import { SendMessageBatchRequest } from 'aws-sdk/clients/sqs';
@injectable()
export class SqsService {
  private sqs: SQS;

  constructor() {
    // Initialize the SQS client
    this.sqs = new SQS({
      region: process.env.AWS_REGION,
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
    });
  }

  async sendMessage(user: User, title: string, message: string): Promise<void> {

    const deviceARN = user.arn ? [user.arn] : [];
    const messageBody = JSON.stringify({email: user.email,  "subject": title, "content": message, "deviceARNs": deviceARN, "sender": '<EMAIL>'});
    const params = {
      MessageBody: messageBody,
      QueueUrl: `${process.env.AWS_SQS_URL}`
    };

    try {
      await this.sqs.sendMessage(params).promise();
      console.log('Message sent successfully.');
    } catch (error) {
      console.error('Error sending message to SQS:', error);
      throw error;
    }
  }

  async sendEmail(email: string, title: string, message: string): Promise<void> {

   
    const messageBody = JSON.stringify({email: email,  "subject": title, "content": message, "sender": '<EMAIL>'});
    const params = {
      MessageBody: messageBody,
      QueueUrl: `${process.env.AWS_SQS_URL}`
    };

    try {
      await this.sqs.sendMessage(params).promise();
      console.log('Message sent successfully.');
    } catch (error) {
      console.error('Error sending message to SQS:', error);
      throw error;
    }
  }

  // async sendBatchMessage(users: User[], title: string, message: string): Promise<void> {


  //   // const messages = JSON.stringify({email: user.email,  "subject": title, "content": message, "deviceARNs": [user.arn]});
  //   const messages : SendMessageBatchRequest[] = users.map(user => ({Id: user.id, MessageBody: JSON.stringify({email: user.email,  "subject": title, "content": message, "deviceARNs": [user.arn]})}))
  //   const params = {
  //     MessageBody: messages,
  //     QueueUrl: `${process.env.AWS_SQS_URL}`
  //   };

  //   try {
  //     await this.sqs.sendMessageBatch(params).promise();
  //     console.log('Message sent successfully.');
  //   } catch (error) {
  //     console.error('Error sending message to SQS:', error);
  //     throw error;
  //   }
  // }
}