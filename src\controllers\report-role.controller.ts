import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {ReportRole} from '../models';
import {ReportRoleRepository} from '../repositories';

export class ReportRoleController {
  constructor(
    @repository(ReportRoleRepository)
    public reportRoleRepository : ReportRoleRepository,
  ) {}

  @post('/report-roles')
  @response(200, {
    description: 'ReportRole model instance',
    content: {'application/json': {schema: getModelSchemaRef(ReportRole)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ReportRole, {
            title: 'NewReportRole',
            exclude: ['id'],
          }),
        },
      },
    })
    reportRole: Omit<ReportRole, 'id'>,
  ): Promise<ReportRole> {
    return this.reportRoleRepository.create(reportRole);
  }

  @get('/report-roles/count')
  @response(200, {
    description: 'ReportRole model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(ReportRole) where?: Where<ReportRole>,
  ): Promise<Count> {
    return this.reportRoleRepository.count(where);
  }

  @get('/report-roles')
  @response(200, {
    description: 'Array of ReportRole model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ReportRole, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(ReportRole) filter?: Filter<ReportRole>,
  ): Promise<ReportRole[]> {
    return this.reportRoleRepository.find(filter);
  }

  @patch('/report-roles')
  @response(200, {
    description: 'ReportRole PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ReportRole, {partial: true}),
        },
      },
    })
    reportRole: ReportRole,
    @param.where(ReportRole) where?: Where<ReportRole>,
  ): Promise<Count> {
    return this.reportRoleRepository.updateAll(reportRole, where);
  }

  @get('/report-roles/{id}')
  @response(200, {
    description: 'ReportRole model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(ReportRole, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(ReportRole, {exclude: 'where'}) filter?: FilterExcludingWhere<ReportRole>
  ): Promise<ReportRole> {
    return this.reportRoleRepository.findById(id, filter);
  }

  @patch('/report-roles/{id}')
  @response(204, {
    description: 'ReportRole PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ReportRole, {partial: true}),
        },
      },
    })
    reportRole: ReportRole,
  ): Promise<void> {
    await this.reportRoleRepository.updateById(id, reportRole);
  }

  @put('/report-roles/{id}')
  @response(204, {
    description: 'ReportRole PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() reportRole: ReportRole,
  ): Promise<void> {
    await this.reportRoleRepository.replaceById(id, reportRole);
  }

  @del('/report-roles/{id}')
  @response(204, {
    description: 'ReportRole DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.reportRoleRepository.deleteById(id);
  }
}
