import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {LocationFour, LocationFourRelations, LocationFive, UserLocationRole} from '../models';
import {LocationFiveRepository} from './location-five.repository';
import {UserLocationRoleRepository} from './user-location-role.repository';

export class LocationFourRepository extends DefaultCrudRepository<
  LocationFour,
  typeof LocationFour.prototype.id,
  LocationFourRelations
> {

  public readonly locationFives: HasManyRepositoryFactory<LocationFive, typeof LocationFour.prototype.id>;

  public readonly userLocationRoles: HasManyRepositoryFactory<UserLocationRole, typeof LocationFour.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('LocationFiveRepository') protected locationFiveRepositoryGetter: Getter<LocationFiveRepository>, @repository.getter('UserLocationRoleRepository') protected userLocationRoleRepositoryGetter: Getter<UserLocationRoleRepository>,
  ) {
    super(LocationFour, dataSource);
    this.userLocationRoles = this.createHasManyRepositoryFactoryFor('userLocationRoles', userLocationRoleRepositoryGetter,);
    this.registerInclusionResolver('userLocationRoles', this.userLocationRoles.inclusionResolver);
    this.locationFives = this.createHasManyRepositoryFactoryFor('locationFives', locationFiveRepositoryGetter,);
    this.registerInclusionResolver('locationFives', this.locationFives.inclusionResolver);
  }
}
