import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {AuditGmsTwo} from '../models';
import {AuditGmsTwoRepository} from '../repositories';

export class AuditGmsTwoController {
  constructor(
    @repository(AuditGmsTwoRepository)
    public auditGmsTwoRepository : AuditGmsTwoRepository,
  ) {}

  @post('/audit-gms-twos')
  @response(200, {
    description: 'AuditGmsTwo model instance',
    content: {'application/json': {schema: getModelSchemaRef(AuditGmsTwo)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AuditGmsTwo, {
            title: 'NewAuditGmsTwo',
            exclude: ['id'],
          }),
        },
      },
    })
    auditGmsTwo: Omit<AuditGmsTwo, 'id'>,
  ): Promise<AuditGmsTwo> {
    return this.auditGmsTwoRepository.create(auditGmsTwo);
  }

  @get('/audit-gms-twos/count')
  @response(200, {
    description: 'AuditGmsTwo model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(AuditGmsTwo) where?: Where<AuditGmsTwo>,
  ): Promise<Count> {
    return this.auditGmsTwoRepository.count(where);
  }

  @get('/audit-gms-twos')
  @response(200, {
    description: 'Array of AuditGmsTwo model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(AuditGmsTwo, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(AuditGmsTwo) filter?: Filter<AuditGmsTwo>,
  ): Promise<AuditGmsTwo[]> {
    return this.auditGmsTwoRepository.find(filter);
  }

  @patch('/audit-gms-twos')
  @response(200, {
    description: 'AuditGmsTwo PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AuditGmsTwo, {partial: true}),
        },
      },
    })
    auditGmsTwo: AuditGmsTwo,
    @param.where(AuditGmsTwo) where?: Where<AuditGmsTwo>,
  ): Promise<Count> {
    return this.auditGmsTwoRepository.updateAll(auditGmsTwo, where);
  }

  @get('/audit-gms-twos/{id}')
  @response(200, {
    description: 'AuditGmsTwo model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(AuditGmsTwo, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(AuditGmsTwo, {exclude: 'where'}) filter?: FilterExcludingWhere<AuditGmsTwo>
  ): Promise<AuditGmsTwo> {
    return this.auditGmsTwoRepository.findById(id, filter);
  }

  @patch('/audit-gms-twos/{id}')
  @response(204, {
    description: 'AuditGmsTwo PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AuditGmsTwo, {partial: true}),
        },
      },
    })
    auditGmsTwo: AuditGmsTwo,
  ): Promise<void> {
    await this.auditGmsTwoRepository.updateById(id, auditGmsTwo);
  }

  @put('/audit-gms-twos/{id}')
  @response(204, {
    description: 'AuditGmsTwo PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() auditGmsTwo: AuditGmsTwo,
  ): Promise<void> {
    await this.auditGmsTwoRepository.replaceById(id, auditGmsTwo);
  }

  @del('/audit-gms-twos/{id}')
  @response(204, {
    description: 'AuditGmsTwo DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.auditGmsTwoRepository.deleteById(id);
  }
}
