import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ReportData,
  LocationThree,
} from '../models';
import {ReportDataRepository} from '../repositories';

export class ReportDataLocationThreeController {
  constructor(
    @repository(ReportDataRepository)
    public reportDataRepository: ReportDataRepository,
  ) { }

  @get('/report-data/{id}/location-three', {
    responses: {
      '200': {
        description: 'LocationThree belonging to ReportData',
        content: {
          'application/json': {
            schema: getModelSchemaRef(LocationThree),
          },
        },
      },
    },
  })
  async getLocationThree(
    @param.path.string('id') id: typeof ReportData.prototype.id,
  ): Promise<LocationThree> {
    return this.reportDataRepository.locationThree(id);
  }
}
