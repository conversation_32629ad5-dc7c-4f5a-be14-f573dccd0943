import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  PermitReport,
  LocationSix,
} from '../models';
import {PermitReportRepository} from '../repositories';

export class PermitReportLocationSixController {
  constructor(
    @repository(PermitReportRepository)
    public permitReportRepository: PermitReportRepository,
  ) { }

  @get('/permit-reports/{id}/location-six', {
    responses: {
      '200': {
        description: 'LocationSix belonging to PermitReport',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(LocationSix)},
          },
        },
      },
    },
  })
  async getLocationSix(
    @param.path.string('id') id: typeof PermitReport.prototype.id,
  ): Promise<LocationSix> {
    return this.permitReportRepository.locationSix(id);
  }
}
