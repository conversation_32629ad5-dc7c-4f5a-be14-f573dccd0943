import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {HazardDescription} from '../models';
import {HazardDescriptionRepository} from '../repositories';

export class HazardDescriptionController {
  constructor(
    @repository(HazardDescriptionRepository)
    public hazardDescriptionRepository : HazardDescriptionRepository,
  ) {}

  @post('/hazard-descriptions')
  @response(200, {
    description: 'HazardDescription model instance',
    content: {'application/json': {schema: getModelSchemaRef(HazardDescription)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(HazardDescription, {
            title: 'NewHazardDescription',
            exclude: ['id'],
          }),
        },
      },
    })
    hazardDescription: Omit<HazardDescription, 'id'>,
  ): Promise<HazardDescription> {
    return this.hazardDescriptionRepository.create(hazardDescription);
  }

  @get('/hazard-descriptions/count')
  @response(200, {
    description: 'HazardDescription model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(HazardDescription) where?: Where<HazardDescription>,
  ): Promise<Count> {
    return this.hazardDescriptionRepository.count(where);
  }

  @get('/hazard-descriptions')
  @response(200, {
    description: 'Array of HazardDescription model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(HazardDescription, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(HazardDescription) filter?: Filter<HazardDescription>,
  ): Promise<HazardDescription[]> {
    return this.hazardDescriptionRepository.find(filter);
  }

  @patch('/hazard-descriptions')
  @response(200, {
    description: 'HazardDescription PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(HazardDescription, {partial: true}),
        },
      },
    })
    hazardDescription: HazardDescription,
    @param.where(HazardDescription) where?: Where<HazardDescription>,
  ): Promise<Count> {
    return this.hazardDescriptionRepository.updateAll(hazardDescription, where);
  }

  @get('/hazard-descriptions/{id}')
  @response(200, {
    description: 'HazardDescription model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(HazardDescription, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(HazardDescription, {exclude: 'where'}) filter?: FilterExcludingWhere<HazardDescription>
  ): Promise<HazardDescription> {
    return this.hazardDescriptionRepository.findById(id, filter);
  }

  @patch('/hazard-descriptions/{id}')
  @response(204, {
    description: 'HazardDescription PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(HazardDescription, {partial: true}),
        },
      },
    })
    hazardDescription: HazardDescription,
  ): Promise<void> {
    await this.hazardDescriptionRepository.updateById(id, hazardDescription);
  }

  @put('/hazard-descriptions/{id}')
  @response(204, {
    description: 'HazardDescription PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() hazardDescription: HazardDescription,
  ): Promise<void> {
    await this.hazardDescriptionRepository.replaceById(id, hazardDescription);
  }

  @del('/hazard-descriptions/{id}')
  @response(204, {
    description: 'HazardDescription DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.hazardDescriptionRepository.deleteById(id);
  }
}
