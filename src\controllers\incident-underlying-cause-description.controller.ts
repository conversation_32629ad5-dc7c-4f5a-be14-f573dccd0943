import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {IncidentUnderlyingCauseDescription} from '../models';
import {IncidentUnderlyingCauseDescriptionRepository} from '../repositories';

export class IncidentUnderlyingCauseDescriptionController {
  constructor(
    @repository(IncidentUnderlyingCauseDescriptionRepository)
    public incidentUnderlyingCauseDescriptionRepository : IncidentUnderlyingCauseDescriptionRepository,
  ) {}

  @post('/incident-underlying-cause-descriptions')
  @response(200, {
    description: 'IncidentUnderlyingCauseDescription model instance',
    content: {'application/json': {schema: getModelSchemaRef(IncidentUnderlyingCauseDescription)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(IncidentUnderlyingCauseDescription, {
            title: 'NewIncidentUnderlyingCauseDescription',
            exclude: ['id'],
          }),
        },
      },
    })
    incidentUnderlyingCauseDescription: Omit<IncidentUnderlyingCauseDescription, 'id'>,
  ): Promise<IncidentUnderlyingCauseDescription> {
    return this.incidentUnderlyingCauseDescriptionRepository.create(incidentUnderlyingCauseDescription);
  }

  @get('/incident-underlying-cause-descriptions/count')
  @response(200, {
    description: 'IncidentUnderlyingCauseDescription model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(IncidentUnderlyingCauseDescription) where?: Where<IncidentUnderlyingCauseDescription>,
  ): Promise<Count> {
    return this.incidentUnderlyingCauseDescriptionRepository.count(where);
  }

  @get('/incident-underlying-cause-descriptions')
  @response(200, {
    description: 'Array of IncidentUnderlyingCauseDescription model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(IncidentUnderlyingCauseDescription, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(IncidentUnderlyingCauseDescription) filter?: Filter<IncidentUnderlyingCauseDescription>,
  ): Promise<IncidentUnderlyingCauseDescription[]> {
    return this.incidentUnderlyingCauseDescriptionRepository.find(filter);
  }

  @patch('/incident-underlying-cause-descriptions')
  @response(200, {
    description: 'IncidentUnderlyingCauseDescription PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(IncidentUnderlyingCauseDescription, {partial: true}),
        },
      },
    })
    incidentUnderlyingCauseDescription: IncidentUnderlyingCauseDescription,
    @param.where(IncidentUnderlyingCauseDescription) where?: Where<IncidentUnderlyingCauseDescription>,
  ): Promise<Count> {
    return this.incidentUnderlyingCauseDescriptionRepository.updateAll(incidentUnderlyingCauseDescription, where);
  }

  @get('/incident-underlying-cause-descriptions/{id}')
  @response(200, {
    description: 'IncidentUnderlyingCauseDescription model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(IncidentUnderlyingCauseDescription, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(IncidentUnderlyingCauseDescription, {exclude: 'where'}) filter?: FilterExcludingWhere<IncidentUnderlyingCauseDescription>
  ): Promise<IncidentUnderlyingCauseDescription> {
    return this.incidentUnderlyingCauseDescriptionRepository.findById(id, filter);
  }

  @patch('/incident-underlying-cause-descriptions/{id}')
  @response(204, {
    description: 'IncidentUnderlyingCauseDescription PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(IncidentUnderlyingCauseDescription, {partial: true}),
        },
      },
    })
    incidentUnderlyingCauseDescription: IncidentUnderlyingCauseDescription,
  ): Promise<void> {
    await this.incidentUnderlyingCauseDescriptionRepository.updateById(id, incidentUnderlyingCauseDescription);
  }

  @put('/incident-underlying-cause-descriptions/{id}')
  @response(204, {
    description: 'IncidentUnderlyingCauseDescription PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() incidentUnderlyingCauseDescription: IncidentUnderlyingCauseDescription,
  ): Promise<void> {
    await this.incidentUnderlyingCauseDescriptionRepository.replaceById(id, incidentUnderlyingCauseDescription);
  }

  @del('/incident-underlying-cause-descriptions/{id}')
  @response(204, {
    description: 'IncidentUnderlyingCauseDescription DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.incidentUnderlyingCauseDescriptionRepository.deleteById(id);
  }
}
