import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  LocationThree,
  LocationFour,
} from '../models';
import {LocationThreeRepository} from '../repositories';
import { authenticate } from '@loopback/authentication';
@authenticate('jwt')
export class LocationThreeLocationFourController {
  constructor(
    @repository(LocationThreeRepository) protected locationThreeRepository: LocationThreeRepository,
  ) { }

  @get('/location-threes/{id}/location-fours', {
    responses: {
      '200': {
        description: 'Array of LocationThree has many LocationFour',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(LocationFour)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<LocationFour>,
  ): Promise<LocationFour[]> {
    return this.locationThreeRepository.locationFours(id).find(filter);
  }

  @post('/location-threes/{id}/location-fours', {
    responses: {
      '200': {
        description: 'LocationThree model instance',
        content: {'application/json': {schema: getModelSchemaRef(LocationFour)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof LocationThree.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(LocationFour, {
            title: 'NewLocationFourInLocationThree',
            exclude: ['id'],
            optional: ['locationThreeId']
          }),
        },
      },
    }) locationFour: Omit<LocationFour, 'id'>,
  ): Promise<LocationFour> {
    return this.locationThreeRepository.locationFours(id).create(locationFour);
  }

  @patch('/location-threes/{id}/location-fours', {
    responses: {
      '200': {
        description: 'LocationThree.LocationFour PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(LocationFour, {partial: true}),
        },
      },
    })
    locationFour: Partial<LocationFour>,
    @param.query.object('where', getWhereSchemaFor(LocationFour)) where?: Where<LocationFour>,
  ): Promise<Count> {
    return this.locationThreeRepository.locationFours(id).patch(locationFour, where);
  }

  @del('/location-threes/{id}/location-fours', {
    responses: {
      '200': {
        description: 'LocationThree.LocationFour DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(LocationFour)) where?: Where<LocationFour>,
  ): Promise<Count> {
    return this.locationThreeRepository.locationFours(id).delete(where);
  }
}
