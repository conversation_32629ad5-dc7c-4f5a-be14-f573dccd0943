import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {HazardCategory, HazardCategoryRelations, HazardType} from '../models';
import {HazardTypeRepository} from './hazard-type.repository';

export class HazardCategoryRepository extends DefaultCrudRepository<
  HazardCategory,
  typeof HazardCategory.prototype.id,
  HazardCategoryRelations
> {

  public readonly hazardTypes: HasManyRepositoryFactory<HazardType, typeof HazardCategory.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('HazardTypeRepository') protected hazardTypeRepositoryGetter: Getter<HazardTypeRepository>,
  ) {
    super(HazardCategory, dataSource);
    this.hazardTypes = this.createHasManyRepositoryFactoryFor('hazardTypes', hazardTypeRepositoryGetter,);
    this.registerInclusionResolver('hazardTypes', this.hazardTypes.inclusionResolver);
  }
}
