import {Entity, model, property} from '@loopback/repository';

@model()
export class HazardGms extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'string',
  })
  hazardDescriptionId?: string;

  constructor(data?: Partial<HazardGms>) {
    super(data);
  }
}

export interface HazardGmsRelations {
  // describe navigational properties here
}

export type HazardGmsWithRelations = HazardGms & HazardGmsRelations;
