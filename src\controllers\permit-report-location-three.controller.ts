import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  PermitReport,
  LocationThree,
} from '../models';
import {PermitReportRepository} from '../repositories';

export class PermitReportLocationThreeController {
  constructor(
    @repository(PermitReportRepository)
    public permitReportRepository: PermitReportRepository,
  ) { }

  @get('/permit-reports/{id}/location-three', {
    responses: {
      '200': {
        description: 'LocationThree belonging to PermitReport',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(LocationThree)},
          },
        },
      },
    },
  })
  async getLocationThree(
    @param.path.string('id') id: typeof PermitReport.prototype.id,
  ): Promise<LocationThree> {
    return this.permitReportRepository.locationThree(id);
  }
}
