import { Entity, model, property, hasMany } from '@loopback/repository';
import { HumanTwo } from './human-two.model';

@model()
export class HumanOne extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;
  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'number',
  })
  order?: number;

  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'string',
  })
  modified?: string;
  @hasMany(() => HumanTwo)
  humanTwos: HumanTwo[];

  constructor(data?: Partial<HumanOne>) {
    super(data);
  }
}

export interface HumanOneRelations {
  // describe navigational properties here
}

export type HumanOneWithRelations = HumanOne & HumanOneRelations;
