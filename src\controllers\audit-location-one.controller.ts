import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  Audit,
  LocationOne,
} from '../models';
import {AuditRepository} from '../repositories';

export class AuditLocationOneController {
  constructor(
    @repository(AuditRepository)
    public auditRepository: AuditRepository,
  ) { }

  @get('/audits/{id}/location-one', {
    responses: {
      '200': {
        description: 'LocationOne belonging to Audit',
        content: {
          'application/json': {
            schema: getModelSchemaRef(LocationOne),
          },
        },
      },
    },
  })
  async getLocationOne(
    @param.path.string('id') id: typeof Audit.prototype.id,
  ): Promise<LocationOne> {
    return this.auditRepository.locationOne(id);
  }
}
