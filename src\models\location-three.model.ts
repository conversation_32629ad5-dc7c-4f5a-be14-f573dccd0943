import { Entity, model, property, hasMany} from '@loopback/repository';
import {LocationFour} from './location-four.model';
import {UserLocationRole} from './user-location-role.model';

@model()
export class LocationThree extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'number',
  })
  order?: number;
  
  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'string',
  })
  modified?: string;

  @property({
    type: 'string',
  })
  locationTwoId?: string;

  @hasMany(() => LocationFour)
  locationFours: LocationFour[];

  @hasMany(() => UserLocationRole)
  userLocationRoles: UserLocationRole[];

  constructor(data?: Partial<LocationThree>) {
    super(data);
  }
}

export interface LocationThreeRelations {
  // describe navigational properties here
}

export type LocationThreeWithRelations = LocationThree & LocationThreeRelations;
