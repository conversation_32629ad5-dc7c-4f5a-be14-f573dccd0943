import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {IncidentUnderlyingCause, IncidentUnderlyingCauseRelations, IncidentUnderlyingCauseType} from '../models';
import {IncidentUnderlyingCauseTypeRepository} from './incident-underlying-cause-type.repository';

export class IncidentUnderlyingCauseRepository extends DefaultCrudRepository<
  IncidentUnderlyingCause,
  typeof IncidentUnderlyingCause.prototype.id,
  IncidentUnderlyingCauseRelations
> {

  public readonly incidentUnderlyingCauseTypes: HasManyRepositoryFactory<IncidentUnderlyingCauseType, typeof IncidentUnderlyingCause.prototype.id>;

  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource, @repository.getter('IncidentUnderlyingCauseTypeRepository') protected incidentUnderlyingCauseTypeRepositoryGetter: Getter<IncidentUnderlyingCauseTypeRepository>,
  ) {
    super(IncidentUnderlyingCause, dataSource);
    this.incidentUnderlyingCauseTypes = this.createHasManyRepositoryFactoryFor('incidentUnderlyingCauseTypes', incidentUnderlyingCauseTypeRepositoryGetter,);
    this.registerInclusionResolver('incidentUnderlyingCauseTypes', this.incidentUnderlyingCauseTypes.inclusionResolver);
  }
}
