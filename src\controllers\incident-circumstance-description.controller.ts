import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {IncidentCircumstanceDescription} from '../models';
import {IncidentCircumstanceDescriptionRepository} from '../repositories';

export class IncidentCircumstanceDescriptionController {
  constructor(
    @repository(IncidentCircumstanceDescriptionRepository)
    public incidentCircumstanceDescriptionRepository : IncidentCircumstanceDescriptionRepository,
  ) {}

  @post('/incident-circumstance-descriptions')
  @response(200, {
    description: 'IncidentCircumstanceDescription model instance',
    content: {'application/json': {schema: getModelSchemaRef(IncidentCircumstanceDescription)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(IncidentCircumstanceDescription, {
            title: 'NewIncidentCircumstanceDescription',
            exclude: ['id'],
          }),
        },
      },
    })
    incidentCircumstanceDescription: Omit<IncidentCircumstanceDescription, 'id'>,
  ): Promise<IncidentCircumstanceDescription> {
    return this.incidentCircumstanceDescriptionRepository.create(incidentCircumstanceDescription);
  }

  @get('/incident-circumstance-descriptions/count')
  @response(200, {
    description: 'IncidentCircumstanceDescription model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(IncidentCircumstanceDescription) where?: Where<IncidentCircumstanceDescription>,
  ): Promise<Count> {
    return this.incidentCircumstanceDescriptionRepository.count(where);
  }

  @get('/incident-circumstance-descriptions')
  @response(200, {
    description: 'Array of IncidentCircumstanceDescription model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(IncidentCircumstanceDescription, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(IncidentCircumstanceDescription) filter?: Filter<IncidentCircumstanceDescription>,
  ): Promise<IncidentCircumstanceDescription[]> {
    return this.incidentCircumstanceDescriptionRepository.find(filter);
  }

  @patch('/incident-circumstance-descriptions')
  @response(200, {
    description: 'IncidentCircumstanceDescription PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(IncidentCircumstanceDescription, {partial: true}),
        },
      },
    })
    incidentCircumstanceDescription: IncidentCircumstanceDescription,
    @param.where(IncidentCircumstanceDescription) where?: Where<IncidentCircumstanceDescription>,
  ): Promise<Count> {
    return this.incidentCircumstanceDescriptionRepository.updateAll(incidentCircumstanceDescription, where);
  }

  @get('/incident-circumstance-descriptions/{id}')
  @response(200, {
    description: 'IncidentCircumstanceDescription model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(IncidentCircumstanceDescription, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(IncidentCircumstanceDescription, {exclude: 'where'}) filter?: FilterExcludingWhere<IncidentCircumstanceDescription>
  ): Promise<IncidentCircumstanceDescription> {
    return this.incidentCircumstanceDescriptionRepository.findById(id, filter);
  }

  @patch('/incident-circumstance-descriptions/{id}')
  @response(204, {
    description: 'IncidentCircumstanceDescription PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(IncidentCircumstanceDescription, {partial: true}),
        },
      },
    })
    incidentCircumstanceDescription: IncidentCircumstanceDescription,
  ): Promise<void> {
    await this.incidentCircumstanceDescriptionRepository.updateById(id, incidentCircumstanceDescription);
  }

  @put('/incident-circumstance-descriptions/{id}')
  @response(204, {
    description: 'IncidentCircumstanceDescription PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() incidentCircumstanceDescription: IncidentCircumstanceDescription,
  ): Promise<void> {
    await this.incidentCircumstanceDescriptionRepository.replaceById(id, incidentCircumstanceDescription);
  }

  @del('/incident-circumstance-descriptions/{id}')
  @response(204, {
    description: 'IncidentCircumstanceDescription DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.incidentCircumstanceDescriptionRepository.deleteById(id);
  }
}
