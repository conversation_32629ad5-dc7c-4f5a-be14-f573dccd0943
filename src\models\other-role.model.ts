import { Entity, model, property } from '@loopback/repository';

@model()
export class OtherRole extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'any',
  })
  permissions?: any;

  @property({
    type: 'number',
  })
  order?: number;

  @property({
    type: 'date',
  })
  created?: string;
  constructor(data?: Partial<OtherRole>) {
    super(data);
  }
}

export interface OtherRoleRelations {
  // describe navigational properties here
}

export type OtherRoleWithRelations = OtherRole & OtherRoleRelations;
