import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {AuditRole, AuditRoleRelations} from '../models';

export class AuditRoleRepository extends DefaultCrudRepository<
  AuditRole,
  typeof AuditRole.prototype.id,
  AuditRoleRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(AuditRole, dataSource);
  }
}
