import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {IncidentUnderlyingCauseType} from '../models';
import {IncidentUnderlyingCauseTypeRepository} from '../repositories';

export class IncidentUnderlyingCauseTypeController {
  constructor(
    @repository(IncidentUnderlyingCauseTypeRepository)
    public incidentUnderlyingCauseTypeRepository : IncidentUnderlyingCauseTypeRepository,
  ) {}

  @post('/incident-underlying-cause-types')
  @response(200, {
    description: 'IncidentUnderlyingCauseType model instance',
    content: {'application/json': {schema: getModelSchemaRef(IncidentUnderlyingCauseType)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(IncidentUnderlyingCauseType, {
            title: 'NewIncidentUnderlyingCauseType',
            exclude: ['id'],
          }),
        },
      },
    })
    incidentUnderlyingCauseType: Omit<IncidentUnderlyingCauseType, 'id'>,
  ): Promise<IncidentUnderlyingCauseType> {
    return this.incidentUnderlyingCauseTypeRepository.create(incidentUnderlyingCauseType);
  }

  @get('/incident-underlying-cause-types/count')
  @response(200, {
    description: 'IncidentUnderlyingCauseType model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(IncidentUnderlyingCauseType) where?: Where<IncidentUnderlyingCauseType>,
  ): Promise<Count> {
    return this.incidentUnderlyingCauseTypeRepository.count(where);
  }

  @get('/incident-underlying-cause-types')
  @response(200, {
    description: 'Array of IncidentUnderlyingCauseType model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(IncidentUnderlyingCauseType, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(IncidentUnderlyingCauseType) filter?: Filter<IncidentUnderlyingCauseType>,
  ): Promise<IncidentUnderlyingCauseType[]> {
    return this.incidentUnderlyingCauseTypeRepository.find(filter);
  }

  @patch('/incident-underlying-cause-types')
  @response(200, {
    description: 'IncidentUnderlyingCauseType PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(IncidentUnderlyingCauseType, {partial: true}),
        },
      },
    })
    incidentUnderlyingCauseType: IncidentUnderlyingCauseType,
    @param.where(IncidentUnderlyingCauseType) where?: Where<IncidentUnderlyingCauseType>,
  ): Promise<Count> {
    return this.incidentUnderlyingCauseTypeRepository.updateAll(incidentUnderlyingCauseType, where);
  }

  @get('/incident-underlying-cause-types/{id}')
  @response(200, {
    description: 'IncidentUnderlyingCauseType model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(IncidentUnderlyingCauseType, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(IncidentUnderlyingCauseType, {exclude: 'where'}) filter?: FilterExcludingWhere<IncidentUnderlyingCauseType>
  ): Promise<IncidentUnderlyingCauseType> {
    return this.incidentUnderlyingCauseTypeRepository.findById(id, filter);
  }

  @patch('/incident-underlying-cause-types/{id}')
  @response(204, {
    description: 'IncidentUnderlyingCauseType PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(IncidentUnderlyingCauseType, {partial: true}),
        },
      },
    })
    incidentUnderlyingCauseType: IncidentUnderlyingCauseType,
  ): Promise<void> {
    await this.incidentUnderlyingCauseTypeRepository.updateById(id, incidentUnderlyingCauseType);
  }

  @put('/incident-underlying-cause-types/{id}')
  @response(204, {
    description: 'IncidentUnderlyingCauseType PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() incidentUnderlyingCauseType: IncidentUnderlyingCauseType,
  ): Promise<void> {
    await this.incidentUnderlyingCauseTypeRepository.replaceById(id, incidentUnderlyingCauseType);
  }

  @del('/incident-underlying-cause-types/{id}')
  @response(204, {
    description: 'IncidentUnderlyingCauseType DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.incidentUnderlyingCauseTypeRepository.deleteById(id);
  }
}
