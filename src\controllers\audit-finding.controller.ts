import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {AuditFinding} from '../models';
import {AuditFindingRepository} from '../repositories';

export class AuditFindingController {
  constructor(
    @repository(AuditFindingRepository)
    public auditFindingRepository : AuditFindingRepository,
  ) {}

  @post('/audit-findings')
  @response(200, {
    description: 'AuditFinding model instance',
    content: {'application/json': {schema: getModelSchemaRef(AuditFinding)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AuditFinding, {
            title: 'NewAuditFinding',
            exclude: ['id'],
          }),
        },
      },
    })
    auditFinding: Omit<AuditFinding, 'id'>,
  ): Promise<AuditFinding> {
    return this.auditFindingRepository.create(auditFinding);
  }

  @get('/audit-findings/count')
  @response(200, {
    description: 'AuditFinding model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(AuditFinding) where?: Where<AuditFinding>,
  ): Promise<Count> {
    return this.auditFindingRepository.count(where);
  }

  @get('/audit-findings')
  @response(200, {
    description: 'Array of AuditFinding model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(AuditFinding, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(AuditFinding) filter?: Filter<AuditFinding>,
  ): Promise<AuditFinding[]> {
    return this.auditFindingRepository.find(filter);
  }

  @patch('/audit-findings')
  @response(200, {
    description: 'AuditFinding PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AuditFinding, {partial: true}),
        },
      },
    })
    auditFinding: AuditFinding,
    @param.where(AuditFinding) where?: Where<AuditFinding>,
  ): Promise<Count> {
    return this.auditFindingRepository.updateAll(auditFinding, where);
  }

  @get('/audit-findings/{id}')
  @response(200, {
    description: 'AuditFinding model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(AuditFinding, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(AuditFinding, {exclude: 'where'}) filter?: FilterExcludingWhere<AuditFinding>
  ): Promise<AuditFinding> {
    return this.auditFindingRepository.findById(id, filter);
  }

  @patch('/audit-findings/{id}')
  @response(204, {
    description: 'AuditFinding PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AuditFinding, {partial: true}),
        },
      },
    })
    auditFinding: AuditFinding,
  ): Promise<void> {
    await this.auditFindingRepository.updateById(id, auditFinding);
  }

  @put('/audit-findings/{id}')
  @response(204, {
    description: 'AuditFinding PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() auditFinding: AuditFinding,
  ): Promise<void> {
    await this.auditFindingRepository.replaceById(id, auditFinding);
  }

  @del('/audit-findings/{id}')
  @response(204, {
    description: 'AuditFinding DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.auditFindingRepository.deleteById(id);
  }
}
