import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {IncidentUnderlyingCause} from '../models';
import {IncidentUnderlyingCauseRepository} from '../repositories';

export class IncidentUnderlyingCauseController {
  constructor(
    @repository(IncidentUnderlyingCauseRepository)
    public incidentUnderlyingCauseRepository : IncidentUnderlyingCauseRepository,
  ) {}

  @post('/incident-underlying-causes')
  @response(200, {
    description: 'IncidentUnderlyingCause model instance',
    content: {'application/json': {schema: getModelSchemaRef(IncidentUnderlyingCause)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(IncidentUnderlyingCause, {
            title: 'NewIncidentUnderlyingCause',
            exclude: ['id'],
          }),
        },
      },
    })
    incidentUnderlyingCause: Omit<IncidentUnderlyingCause, 'id'>,
  ): Promise<IncidentUnderlyingCause> {
    return this.incidentUnderlyingCauseRepository.create(incidentUnderlyingCause);
  }

  @get('/incident-underlying-causes/count')
  @response(200, {
    description: 'IncidentUnderlyingCause model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(IncidentUnderlyingCause) where?: Where<IncidentUnderlyingCause>,
  ): Promise<Count> {
    return this.incidentUnderlyingCauseRepository.count(where);
  }

  @get('/incident-underlying-causes')
  @response(200, {
    description: 'Array of IncidentUnderlyingCause model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(IncidentUnderlyingCause, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(IncidentUnderlyingCause) filter?: Filter<IncidentUnderlyingCause>,
  ): Promise<IncidentUnderlyingCause[]> {
    return this.incidentUnderlyingCauseRepository.find(filter);
  }

  @patch('/incident-underlying-causes')
  @response(200, {
    description: 'IncidentUnderlyingCause PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(IncidentUnderlyingCause, {partial: true}),
        },
      },
    })
    incidentUnderlyingCause: IncidentUnderlyingCause,
    @param.where(IncidentUnderlyingCause) where?: Where<IncidentUnderlyingCause>,
  ): Promise<Count> {
    return this.incidentUnderlyingCauseRepository.updateAll(incidentUnderlyingCause, where);
  }

  @get('/incident-underlying-causes/{id}')
  @response(200, {
    description: 'IncidentUnderlyingCause model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(IncidentUnderlyingCause, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(IncidentUnderlyingCause, {exclude: 'where'}) filter?: FilterExcludingWhere<IncidentUnderlyingCause>
  ): Promise<IncidentUnderlyingCause> {
    return this.incidentUnderlyingCauseRepository.findById(id, filter);
  }

  @patch('/incident-underlying-causes/{id}')
  @response(204, {
    description: 'IncidentUnderlyingCause PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(IncidentUnderlyingCause, {partial: true}),
        },
      },
    })
    incidentUnderlyingCause: IncidentUnderlyingCause,
  ): Promise<void> {
    await this.incidentUnderlyingCauseRepository.updateById(id, incidentUnderlyingCause);
  }

  @put('/incident-underlying-causes/{id}')
  @response(204, {
    description: 'IncidentUnderlyingCause PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() incidentUnderlyingCause: IncidentUnderlyingCause,
  ): Promise<void> {
    await this.incidentUnderlyingCauseRepository.replaceById(id, incidentUnderlyingCause);
  }

  @del('/incident-underlying-causes/{id}')
  @response(204, {
    description: 'IncidentUnderlyingCause DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.incidentUnderlyingCauseRepository.deleteById(id);
  }
}
