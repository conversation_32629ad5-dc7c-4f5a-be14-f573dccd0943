import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  LocationOne,
  LocationTwo,
} from '../models';
import {LocationOneRepository} from '../repositories';
import { authenticate } from '@loopback/authentication';
@authenticate('jwt')
export class LocationOneLocationTwoController {
  constructor(
    @repository(LocationOneRepository) protected locationOneRepository: LocationOneRepository,
  ) { }

  @get('/location-ones/{id}/location-twos', {
    responses: {
      '200': {
        description: 'Array of LocationOne has many LocationTwo',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(LocationTwo)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<LocationTwo>,
  ): Promise<LocationTwo[]> {
    return this.locationOneRepository.locationTwos(id).find(filter);
  }

  @post('/location-ones/{id}/location-twos', {
    responses: {
      '200': {
        description: 'LocationOne model instance',
        content: {'application/json': {schema: getModelSchemaRef(LocationTwo)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof LocationOne.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(LocationTwo, {
            title: 'NewLocationTwoInLocationOne',
            exclude: ['id'],
            optional: ['locationOneId']
          }),
        },
      },
    }) locationTwo: Omit<LocationTwo, 'id'>,
  ): Promise<LocationTwo> {
    return this.locationOneRepository.locationTwos(id).create(locationTwo);
  }

  @patch('/location-ones/{id}/location-twos', {
    responses: {
      '200': {
        description: 'LocationOne.LocationTwo PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(LocationTwo, {partial: true}),
        },
      },
    })
    locationTwo: Partial<LocationTwo>,
    @param.query.object('where', getWhereSchemaFor(LocationTwo)) where?: Where<LocationTwo>,
  ): Promise<Count> {
    return this.locationOneRepository.locationTwos(id).patch(locationTwo, where);
  }

  @del('/location-ones/{id}/location-twos', {
    responses: {
      '200': {
        description: 'LocationOne.LocationTwo DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(LocationTwo)) where?: Where<LocationTwo>,
  ): Promise<Count> {
    return this.locationOneRepository.locationTwos(id).delete(where);
  }
}
